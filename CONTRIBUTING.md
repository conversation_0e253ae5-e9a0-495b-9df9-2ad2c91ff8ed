# Contributing to Next.js + Supabase Template

Thank you for your interest in contributing to this template! This document provides guidelines and information for contributors.

## 🚀 Quick Start for Contributors

1. **Fork the repository**
2. **Clone your fork**:
   ```bash
   git clone https://github.com/yourusername/nextjs-supabase-template.git
   cd nextjs-supabase-template
   ```
3. **Install dependencies**:
   ```bash
   npm install
   ```
4. **Verify installation**:
   ```bash
   npm run verify
   ```
5. **Start development**:
   ```bash
   npm run dev
   ```

## 📋 Development Guidelines

### **Code Standards**
- **TypeScript**: Use strict TypeScript throughout
- **ESLint**: Follow the configured ESLint rules
- **Prettier**: Format code with Prettier
- **Naming**: Use descriptive, clear variable and function names

### **Testing Requirements**
- **Build Test**: Ensure `npm run build` succeeds
- **Type Check**: Ensure `npm run type-check` passes
- **Template Test**: Verify `npm run test:template` works
- **Verification**: Ensure `npm run verify` passes

### **Demo-First Principle**
This template follows a "demo-first" approach:
- **All features must work in demo mode** (without configuration)
- **Graceful fallbacks** when services aren't configured
- **Clear configuration status** indicators
- **No crashes** from missing environment variables

## 🎯 Areas for Contribution

### **High Priority**
- **Bug fixes** and security updates
- **Documentation improvements**
- **Performance optimizations**
- **Accessibility enhancements**

### **Medium Priority**
- **New UI components** (following shadcn/ui patterns)
- **Additional authentication providers**
- **Enhanced PWA features**
- **Developer experience improvements**

### **Low Priority**
- **New integrations** (must maintain demo-first principle)
- **Advanced features** (ensure they don't break simplicity)

## 🔧 Development Process

### **Before Making Changes**
1. **Check existing issues** to avoid duplicates
2. **Create an issue** for major changes
3. **Discuss the approach** before implementing

### **Making Changes**
1. **Create a feature branch**: `git checkout -b feature/your-feature-name`
2. **Make your changes** following the guidelines
3. **Test thoroughly**:
   ```bash
   npm run verify
   npm run type-check
   npm run lint
   npm run build
   npm run test:template
   ```
4. **Update documentation** if needed
5. **Commit with clear messages**

### **Pull Request Process**
1. **Update README** if you've added features
2. **Add tests** for new functionality
3. **Ensure CI passes**
4. **Request review** from maintainers

## 📝 Commit Message Format

Use clear, descriptive commit messages:
```
type(scope): description

Examples:
feat(auth): add GitHub OAuth provider
fix(pwa): resolve push notification subscription issue
docs(readme): update installation instructions
refactor(database): improve error handling
```

## 🐛 Bug Reports

When reporting bugs:
1. **Use the bug report template**
2. **Include reproduction steps**
3. **Provide environment details**
4. **Run `npm run verify`** and include output

## 💡 Feature Requests

When requesting features:
1. **Use the feature request template**
2. **Explain the use case**
3. **Consider the demo-first principle**
4. **Discuss implementation approach**

## 📚 Documentation

Help improve documentation by:
- **Fixing typos** and unclear explanations
- **Adding examples** and use cases
- **Updating outdated information**
- **Translating** to other languages

## 🎉 Recognition

Contributors will be:
- **Listed in README** acknowledgments
- **Mentioned in release notes**
- **Given credit** in commit messages

## 📞 Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and ideas
- **Email**: For security issues or private matters

Thank you for contributing to making this template better! 🚀
