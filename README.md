# �� Next.js + Supabase Clean Template

A **production-ready** Next.js 15 template featuring Supabase backend, authentication, role-based access control, and modern developer tooling. This template is designed to work **immediately out-of-the-box** with demo data, then seamlessly transition to your production setup.

> **🎯 Perfect for:** Building real applications, prototyping, or using as a foundation for your next project!

---

## 🌟 **Template-Ready Features**

### ✅ **Instant Setup - No Configuration Required**
- **Works immediately** after `npm install` and `npm run dev`
- **Demo authentication** - Try all features without any setup
- **Configuration status banner** - Shows exactly what needs to be configured
- **Graceful degradation** - All features work in demo mode

### ✅ **Smart Configuration Detection**
- Automatically detects placeholder vs. real environment variables
- Visual configuration status with clear setup instructions
- Links directly to Supabase and setup documentation
- Only shows warnings when configuration is incomplete

### ✅ **Demo Mode Capabilities**
- Demo user accounts for immediate testing
- All UI components and pages fully functional
- Authentication works with demo credentials
- Admin panel showcases user management features

---

## ✨ Core Features
- **🔐 Authentication & RBAC:** NextAuth.js with Supabase backend, role-based access control
- **⚙️ Admin Panel:** Built-in dashboard for user management and settings
- **🗄️ Database Integration:** Supabase for real-time data, Row Level Security, and live updates  
- **⚡ Real-time Features:** Live updates with Supabase subscriptions
- **👤 User Dashboard:** Clean dashboard ready for your custom features
- **🎨 Modern UI:** Beautiful, responsive design with shadcn/ui components
- **🔒 Security:** Row Level Security (RLS) policies and comprehensive error handling
- **🛠️ Developer Experience:** TypeScript, ESLint, proper project structure, utility scripts
- **📱 PWA Support:** Progressive Web App features with offline capabilities

---

## 🔔 **Push Notifications - Template Ready**

### **✅ Works Immediately - No Setup Required**
- **🎭 Demo VAPID keys** - Push notifications work out-of-the-box
- **📱 Test instantly** - Visit any page and see notification banners
- **🔧 Admin panel** - Complete campaign management included
- **📊 Analytics** - Track delivery rates and user engagement

### **🚀 2-Minute Production Setup**
```bash
# Generate your own VAPID keys
npm run setup:vapid

# Add to admin panel: /admin/pwa → PWA Settings
# Paste the generated keys → Save → Done!
```

### **🎯 Template Buyer Benefits**
- **Skip months of push notification development**
- **Enterprise-grade system included**
- **No third-party dependencies** (no Firebase required)
- **Works on all devices** - iOS, Android, Desktop
- **GDPR compliant** with user preference management

**Test it now:** Go to `/debug-notifications` and click "Test Permission"!

### **🎯 Template Buyers - Instant Testing**
```bash
# After npm install && npm run dev:

# 1. Verify everything works
npm run test:template

# 2. Test push notifications immediately
# Visit: http://localhost:3000/debug-notifications
# Click: "Test Permission" → Allow notifications
# Click: "Test Push Subscription"
# Go to: http://localhost:3000/admin/pwa → "Send Test Notification"
# ✅ Notifications work instantly!

# 📱 iOS Testing Note:
# iOS Safari caches notification permissions aggressively for localhost.
# If blocked: Use Android device, desktop browser, or deploy to production.
# This is iOS behavior, not a template limitation!
```

---

## 🚦 **Quick Start (2 Minutes)**

### **Option 1: Try Demo Mode (Instant)**
```sh
git clone [your-repo-url]
cd your-template
npm install

# Verify installation (optional)
npm run verify

npm run dev

# Test everything works (optional)
npm run test:template
```
**That's it!** Visit `http://localhost:3000` to see the fully functional demo with working push notifications!

> **✅ No dependency conflicts!** This template installs cleanly without any `--legacy-peer-deps` flags or compatibility issues.

### **Option 2: Full Production Setup**
1. **Copy environment template:**
   ```sh
   cp env.example .env.local
   ```

2. **Set up Supabase:**
   - Create project at [supabase.com](https://supabase.com)
   - Copy your project URL and API keys to `.env.local`
   - Run the SQL schema from `supabase-setup.sql`

3. **Configure NextAuth secret:**
   ```sh
   # Use the generated secret or generate your own:
   NEXTAUTH_SECRET=IWoQgbYWTkvogQTZc8dt7md6QLeHNG+tFkovp/LYGy0=
   ```

4. **Create admin user:**
   ```sh
   node scripts/create-admin.js
   ```

5. **Optional: Configure OAuth providers**
   - Add Google/GitHub client credentials to `.env.local`
   - Configure OAuth apps in respective platforms

---

## 🎭 **Demo Credentials**

Try these accounts immediately (no setup required):

| Role | Email | Password | Access |
|------|-------|----------|---------|
| 👑 **Admin** | `<EMAIL>` | `admin123` | Full admin access |
| 👤 **User** | `<EMAIL>` | `user123` | Standard user access |
| 🛡️ **Moderator** | `<EMAIL>` | `mod123` | Moderation privileges |

---

## 📋 **Configuration Status System**

The template includes an intelligent configuration status system:

### **When Not Configured:**
- ⚠️ **Yellow banner** shows what needs setup
- 🔗 **Direct links** to Supabase and documentation  
- 📊 **Demo data** powers all features
- ✅ **Full functionality** in preview mode

### **When Configured:**
- ✅ **Banner disappears** automatically
- 🔄 **Real database** replaces demo data
- 🔐 **Authentication** works with real providers
- 🚀 **Production ready**

---

## 📦 Project Structure
```
├── src/
│   ├── app/
│   │   ├── admin/           # Admin dashboard with user management
│   │   ├── auth/            # Authentication pages (signin/signup)
│   │   ├── dashboard/       # User dashboard (clean foundation)
│   │   └── api/auth/        # NextAuth API routes
│   ├── components/
│   │   ├── ConfigStatus.tsx # Configuration status banner
│   │   └── ui/              # shadcn/ui components
│   ├── lib/
│   │   ├── supabase.ts      # Smart Supabase client with fallbacks
│   │   ├── auth.ts          # NextAuth configuration with demo mode
│   │   ├── database.ts      # Database functions with demo data
│   │   └── utils.ts         # Utility functions
├── scripts/
│   └── create-admin.js      # Admin user creation script
├── supabase-setup.sql       # Complete database schema and policies
└── env.example              # Environment variables template
```

---

## 🔒 Authentication & Roles

### **User Roles**
- **👤 User:** Standard user access with customizable permissions
- **🛡️ Moderator:** Extended permissions for content moderation
- **⚙️ Admin:** Full access to admin panel and system settings

### **Authentication Methods**
- **📧 Email/Password:** Built-in credentials authentication
- **🔵 Google OAuth:** Social login integration
- **⚫ GitHub OAuth:** Developer-friendly authentication
- **🎭 Demo Mode:** Works without any OAuth setup

### **Protected Routes**
- `/dashboard` - Requires authentication
- `/admin` - Requires admin role  
- `/auth/*` - Public authentication pages

---

## 🗄️ Supabase Integration

### **Database Schema**
- **👥 Users:** Extended profiles with role-based access
- **⚙️ Settings:** Dynamic application configuration
- **🔍 Audit Logs:** Complete activity tracking
- **📊 Analytics:** User behavior and system metrics

### **Smart Features**
- **🔄 Real-time updates** - Live data synchronization
- **🔒 Row Level Security** - Database-level permissions
- **📱 Demo data fallbacks** - Works without database
- **⚡ Optimistic updates** - Smooth user experience

---

## 🛠️ Developer Experience

### **Template Benefits**
- **⚡ Instant preview** - See results immediately
- **📊 Live configuration status** - Know exactly what to configure
- **🔧 Error-free setup** - No crashes from missing credentials
- **📖 Clear documentation** - Step-by-step instructions
- **🎯 Production-ready** - Deploy with confidence

### **Built-in Scripts**
```sh
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Code quality checks
npm run verify       # Verify installation integrity
npm run test:template # Test all template features
npm run setup:vapid  # Generate VAPID keys for push notifications
node scripts/create-admin.js  # Create admin user
```

### **TypeScript Support**
- Full type safety for database operations
- Auto-generated Supabase types
- NextAuth.js type extensions
- Comprehensive error handling

---

## 🚀 Deployment

### **Environment Variables**

**Required for production:**
```env
# Supabase (Required)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# NextAuth (Required)  
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your_secure_secret_here
```

**Optional OAuth providers:**
```env
# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth (Optional)
GITHUB_CLIENT_ID=your_github_client_id  
GITHUB_CLIENT_SECRET=your_github_client_secret
```

### **Deployment Platforms**
- **Vercel** (Recommended) - Zero-config deployment
- **Netlify** - Easy static hosting with serverless functions
- **Railway** - Full-stack deployment platform
- **Any Node.js host** - Standard Next.js app

---

## 🎨 Customization

### **Dashboard Customization**
The user dashboard (`/dashboard`) is intentionally minimal and provides:
- Welcome message and user info
- Profile management card
- Quick actions section
- Admin panel access (for admins)

**Add your features here:**
- Replace quick actions with your app's functionality
- Add feature cards, forms, or data visualizations
- Customize the layout for your specific use case

### **Styling**
- **shadcn/ui components** - Professional, accessible UI
- **Tailwind CSS** - Utility-first styling
- **Responsive design** - Mobile-first approach
- **Dark mode ready** - Easy theme switching

### **Extending Features**
- **Database tables** - Add to `supabase-setup.sql`
- **Auth providers** - Configure in `src/lib/auth.ts`  
- **Admin features** - Extend `/admin` pages
- **User features** - Add to `/dashboard` pages
- **Demo data** - Update `src/lib/supabase.ts`

---

## 💼 **Perfect For**

### **Template Sellers**
- ✅ Works immediately for buyers
- ✅ Professional configuration system
- ✅ Clear setup documentation
- ✅ No crashes or errors out-of-the-box

### **Developers**
- ✅ Skip the boilerplate setup
- ✅ Production-ready architecture
- ✅ Modern best practices
- ✅ Clean foundation for any application

### **Agencies**
- ✅ Reliable starting point for client projects
- ✅ Professional presentation
- ✅ Easy client handoff
- ✅ Scalable foundation

---

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **🚨 Build Errors**
```bash
# If you encounter TypeScript errors:
npm run type-check

# If you encounter ESLint errors:
npm run lint

# Clean build (if needed):
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

#### **📱 Push Notifications Not Working**
1. **Check HTTPS**: Push notifications require HTTPS (except localhost)
2. **Browser Support**: Test in Chrome/Firefox first
3. **iOS Safari**: Use Android/Desktop for testing (iOS has strict localhost caching)
4. **VAPID Keys**: Ensure keys are configured in admin panel

#### **🔐 Authentication Issues**
1. **Demo Mode**: Use provided demo credentials first
2. **Environment Variables**: Check `.env.local` configuration
3. **Supabase Setup**: Verify database schema is imported
4. **NextAuth Secret**: Ensure `NEXTAUTH_SECRET` is set

#### **🗄️ Database Connection**
1. **Demo Mode Works**: Template functions without database
2. **Supabase URL**: Check project URL format
3. **API Keys**: Verify anon and service role keys
4. **RLS Policies**: Ensure policies are enabled

### **Getting Help**
- **Template Test**: Run `npm run test:template` for diagnostics
- **Configuration Status**: Check the yellow banner for missing setup
- **Demo Mode**: All features work without any configuration

---

## 📝 License
**MIT License** - Use for personal or commercial projects!

---

## 🆕 **Latest Updates**

### **v3.0 - Clean Template Release**
- ✅ **Removed blog/post functionality** - Clean foundation for real apps
- ✅ **Streamlined dashboard** - Ready for your custom features
- ✅ **Simplified admin panel** - Focus on user management and settings
- ✅ **Cleaner database schema** - Only essential tables included

### **Previous Features**
- ✅ Complete Supabase integration with RLS
- ✅ NextAuth.js authentication with multiple providers
- ✅ Admin panel with user and settings management
- ✅ Real-time dashboard with clean architecture
- ✅ TypeScript throughout with proper type safety
- ✅ shadcn/ui components for beautiful design
- ✅ PWA support with offline capabilities

---

**🎉 Ready to build something amazing? This template provides the perfect foundation!**
