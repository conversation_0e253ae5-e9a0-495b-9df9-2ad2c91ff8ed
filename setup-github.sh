#!/bin/bash

# GitHub Repository Setup Script
# Usage: ./setup-github.sh yourusername

if [ -z "$1" ]; then
    echo "❌ Error: Please provide your GitHub username"
    echo "Usage: ./setup-github.sh yourusername"
    echo "Example: ./setup-github.sh johndoe"
    exit 1
fi

USERNAME=$1
REPO_NAME="nextjs-supabase-template"
REPO_URL="https://github.com/$USERNAME/$REPO_NAME.git"

echo "🚀 Setting up GitHub repository..."
echo "📋 Repository: $REPO_URL"
echo ""

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "❌ Error: Git repository not initialized"
    echo "Run: git init"
    exit 1
fi

# Check if we have commits
if ! git log --oneline -n 1 > /dev/null 2>&1; then
    echo "❌ Error: No commits found"
    echo "Run: git add . && git commit -m 'Initial commit'"
    exit 1
fi

# Add remote origin
echo "🔗 Adding remote origin..."
if git remote get-url origin > /dev/null 2>&1; then
    echo "⚠️  Remote origin already exists. Removing..."
    git remote remove origin
fi

git remote add origin $REPO_URL

# Set main branch
echo "🌿 Setting main branch..."
git branch -M main

# Push to GitHub
echo "📤 Pushing to GitHub..."
if git push -u origin main; then
    echo ""
    echo "🎉 Success! Repository pushed to GitHub"
    echo "📋 Repository URL: https://github.com/$USERNAME/$REPO_NAME"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Visit: https://github.com/$USERNAME/$REPO_NAME"
    echo "2. Go to Settings → General → Features"
    echo "3. Check 'Template repository' to make it a template"
    echo "4. Add topics: nextjs, supabase, template, pwa, push-notifications"
    echo "5. Create your first release with: npm run prepare-release"
    echo ""
    echo "🚀 Your template is now live on GitHub!"
else
    echo ""
    echo "❌ Error: Failed to push to GitHub"
    echo "💡 Make sure you:"
    echo "1. Created the repository on GitHub: https://github.com/new"
    echo "2. Used the correct username: $USERNAME"
    echo "3. Have push access to the repository"
    echo ""
    echo "🔧 Manual commands:"
    echo "git remote add origin $REPO_URL"
    echo "git push -u origin main"
fi
