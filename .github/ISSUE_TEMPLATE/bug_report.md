---
name: Bug report
about: Create a report to help us improve
title: ''
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
- OS: [e.g. macOS, Windows, Linux]
- Browser [e.g. chrome, safari]
- Node.js version [e.g. 18.17.0]
- Template version [e.g. 1.0.0]

**Installation Method**
- [ ] Standard npm install
- [ ] Used --legacy-peer-deps
- [ ] Cloned from GitHub
- [ ] Downloaded as ZIP

**Configuration Status**
- [ ] Demo mode (no configuration)
- [ ] Supabase configured
- [ ] OAuth providers configured
- [ ] Push notifications configured

**Additional context**
Add any other context about the problem here.

**Verification Results**
Please run `npm run verify` and paste the output:
```
[Paste verification output here]
```
