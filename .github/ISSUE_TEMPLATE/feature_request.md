---
name: Feature request
about: Suggest an idea for this project
title: ''
labels: enhancement
assignees: ''

---

**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.

**Template Area**
Which part of the template would this feature affect?
- [ ] Authentication system
- [ ] Database/Supabase integration
- [ ] PWA functionality
- [ ] Push notifications
- [ ] Admin panel
- [ ] UI/UX components
- [ ] Developer experience
- [ ] Documentation
- [ ] Other (please specify)

**Implementation Complexity**
How complex do you think this feature would be to implement?
- [ ] Simple (few lines of code)
- [ ] Medium (new component/function)
- [ ] Complex (architectural changes)
- [ ] Not sure
