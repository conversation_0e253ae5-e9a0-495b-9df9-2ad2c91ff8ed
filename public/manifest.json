{"name": "Next.js + Supabase Advanced Template", "short_name": "NextJS Template", "description": "A production-ready Next.js 15 template featuring Supabase backend, authentication, role-based access control, and modern developer tooling.", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#2563eb", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["business", "productivity", "utilities"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "Go to user dashboard", "url": "/dashboard", "icons": [{"src": "/icons/icon-192x192.png", "sizes": "192x192"}]}, {"name": "Admin Panel", "short_name": "Admin", "description": "Access admin panel", "url": "/admin", "icons": [{"src": "/icons/icon-192x192.png", "sizes": "192x192"}]}], "screenshots": [{"src": "/screenshots/mobile-screenshot.png", "sizes": "640x1136", "type": "image/png", "form_factor": "narrow"}, {"src": "/screenshots/desktop-screenshot.png", "sizes": "1280x800", "type": "image/png", "form_factor": "wide"}]}