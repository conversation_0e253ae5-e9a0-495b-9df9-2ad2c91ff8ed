import type { NextConfig } from "next";
import { withSentryConfig } from "@sentry/nextjs";
import withBundleAnalyzer from '@next/bundle-analyzer';

const nextConfig: NextConfig = {
  // PWA configuration
  async headers() {
    return [
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
          {
            key: 'Service-Worker-Allowed',
            value: '/',
          },
        ],
      },
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/icons/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  
  // Enable static optimization
  output: 'standalone',
  
  // Simplified experimental config
  experimental: {
    // Temporarily disabled optimizeCss to fix critters dependency issue
    // optimizeCss: true,
  },

  images: {
    domains: ['your-image-domains.com'], // Add domains if needed
    minimumCacheTTL: 60,
  },
};

export default withSentryConfig(withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})(nextConfig), {
  silent: true, // Suppresses all Sentry logs
  org: "your-org", // Replace with your Sentry org
  project: "your-project", // Replace with your Sentry project
});
