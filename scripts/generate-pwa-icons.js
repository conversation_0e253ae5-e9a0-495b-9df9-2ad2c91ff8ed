#!/usr/bin/env node

// Simple script to generate PWA icons
// This creates basic colored square icons with text

const fs = require('fs');
const path = require('path');

// Icon sizes needed for PWA
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Colors
const backgroundColor = '#2563eb'; // Blue
const textColor = '#ffffff'; // White

console.log('🎨 Generating PWA icons...');

// Check if we have sharp available for better quality
let sharp;
try {
  sharp = require('sharp');
  console.log('📸 Using Sharp for high-quality icons');
} catch (e) {
  console.log('📝 Sharp not available, creating SVG placeholders');
}

async function generateIcons() {
  const iconsDir = path.join(__dirname, '../public/icons');
  
  // Ensure icons directory exists
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }

  for (const size of sizes) {
    if (sharp) {
      // Generate high-quality PNG with Sharp
      await generateWithSharp(size, iconsDir);
    } else {
      // Generate SVG placeholder
      generateSVGIcon(size, iconsDir);
    }
  }
  
  console.log('✅ PWA icons generated successfully!');
  console.log(`📁 Icons saved to: ${iconsDir}`);
  console.log('📱 Your PWA is ready for installation!');
}

async function generateWithSharp(size, iconsDir) {
  const svg = createSVG(size);
  const outputPath = path.join(iconsDir, `icon-${size}x${size}.png`);
  
  try {
    await sharp(Buffer.from(svg))
      .png()
      .toFile(outputPath);
    console.log(`✅ Generated ${size}x${size} PNG icon`);
  } catch (error) {
    console.error(`❌ Error generating ${size}x${size} icon:`, error.message);
    // Fallback to SVG
    generateSVGIcon(size, iconsDir);
  }
}

function generateSVGIcon(size, iconsDir) {
  const svg = createSVG(size);
  const outputPath = path.join(iconsDir, `icon-${size}x${size}.svg`);
  
  fs.writeFileSync(outputPath, svg);
  console.log(`✅ Generated ${size}x${size} SVG icon`);
}

function createSVG(size) {
  const fontSize = Math.floor(size * 0.25);
  const iconSize = Math.floor(size * 0.4);
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="${size}" height="${size}" fill="${backgroundColor}" rx="${size * 0.2}"/>
  
  <!-- App Icon - House/Home symbol -->
  <g transform="translate(${size * 0.3}, ${size * 0.25})">
    <path d="M${iconSize * 0.5} 0 L${iconSize} ${iconSize * 0.4} L${iconSize * 0.8} ${iconSize * 0.4} L${iconSize * 0.8} ${iconSize} L${iconSize * 0.2} ${iconSize} L${iconSize * 0.2} ${iconSize * 0.4} L0 ${iconSize * 0.4} Z" 
          fill="${textColor}" 
          stroke="${textColor}" 
          stroke-width="2"/>
    <rect x="${iconSize * 0.35}" y="${iconSize * 0.55}" width="${iconSize * 0.3}" height="${iconSize * 0.45}" fill="${backgroundColor}"/>
  </g>
  
  <!-- App name -->
  <text x="${size/2}" y="${size * 0.85}" 
        text-anchor="middle" 
        fill="${textColor}" 
        font-family="system-ui, -apple-system, sans-serif" 
        font-size="${fontSize}" 
        font-weight="bold">PWA</text>
</svg>`;
}

// Run the generator
generateIcons().catch(console.error); 