#!/usr/bin/env node

/**
 * Installation Verification Script
 * Verifies that the template is properly installed and ready to use
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Template Installation...\n');

const checks = [];

// Check 1: Package.json exists and has correct dependencies
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const requiredDeps = [
    'next',
    'next-auth',
    '@supabase/supabase-js',
    'react',
    'react-dom'
  ];

  const requiredDevDeps = [
    'typescript',
    '@types/node',
    '@types/react'
  ];

  const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
  const missingDevDeps = requiredDevDeps.filter(dep => !packageJson.devDependencies[dep]);
  
  if (missingDeps.length === 0 && missingDevDeps.length === 0) {
    checks.push({ name: 'Dependencies', status: '✅ PASS', message: 'All required dependencies present' });
  } else {
    const missing = [...missingDeps, ...missingDevDeps];
    checks.push({ name: 'Dependencies', status: '❌ FAIL', message: `Missing: ${missing.join(', ')}` });
  }
} catch (error) {
  checks.push({ name: 'Dependencies', status: '❌ FAIL', message: 'Could not read package.json' });
}

// Check 2: TypeScript configuration
try {
  const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
  if (tsConfig.compilerOptions && tsConfig.compilerOptions.strict) {
    checks.push({ name: 'TypeScript', status: '✅ PASS', message: 'TypeScript configured with strict mode' });
  } else {
    checks.push({ name: 'TypeScript', status: '⚠️ WARN', message: 'TypeScript not in strict mode' });
  }
} catch (error) {
  checks.push({ name: 'TypeScript', status: '❌ FAIL', message: 'Could not read tsconfig.json' });
}

// Check 3: Environment template
try {
  const envExample = fs.readFileSync('env.example', 'utf8');
  if (envExample.includes('NEXT_PUBLIC_SUPABASE_URL') && envExample.includes('NEXTAUTH_SECRET')) {
    checks.push({ name: 'Environment Template', status: '✅ PASS', message: 'env.example contains required variables' });
  } else {
    checks.push({ name: 'Environment Template', status: '❌ FAIL', message: 'env.example missing required variables' });
  }
} catch (error) {
  checks.push({ name: 'Environment Template', status: '❌ FAIL', message: 'Could not read env.example' });
}

// Check 4: Key directories exist
const requiredDirs = [
  'src/app',
  'src/components', 
  'src/lib',
  'scripts',
  'public'
];

const missingDirs = requiredDirs.filter(dir => !fs.existsSync(dir));

if (missingDirs.length === 0) {
  checks.push({ name: 'Directory Structure', status: '✅ PASS', message: 'All required directories present' });
} else {
  checks.push({ name: 'Directory Structure', status: '❌ FAIL', message: `Missing: ${missingDirs.join(', ')}` });
}

// Check 5: Key files exist
const requiredFiles = [
  'src/lib/auth.ts',
  'src/lib/supabase.ts',
  'src/lib/database.ts',
  'src/app/page.tsx',
  'supabase-setup.sql'
];

const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));

if (missingFiles.length === 0) {
  checks.push({ name: 'Core Files', status: '✅ PASS', message: 'All core files present' });
} else {
  checks.push({ name: 'Core Files', status: '❌ FAIL', message: `Missing: ${missingFiles.join(', ')}` });
}

// Check 6: No dependency conflicts (check for problematic packages)
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check if the problematic Supabase adapter is removed
  if (packageJson.dependencies['@next-auth/supabase-adapter']) {
    checks.push({ name: 'Dependency Conflicts', status: '⚠️ WARN', message: 'Supabase adapter may cause conflicts with NextAuth v5' });
  } else {
    checks.push({ name: 'Dependency Conflicts', status: '✅ PASS', message: 'No known dependency conflicts' });
  }
} catch (error) {
  checks.push({ name: 'Dependency Conflicts', status: '❌ FAIL', message: 'Could not check dependencies' });
}

// Display results
console.log('📋 Installation Verification Results:\n');

checks.forEach(check => {
  console.log(`${check.status} ${check.name}: ${check.message}`);
});

const passCount = checks.filter(c => c.status.includes('✅')).length;
const warnCount = checks.filter(c => c.status.includes('⚠️')).length;
const failCount = checks.filter(c => c.status.includes('❌')).length;

console.log('\n📊 Summary:');
console.log(`✅ Passed: ${passCount}`);
console.log(`⚠️ Warnings: ${warnCount}`);
console.log(`❌ Failed: ${failCount}`);

if (failCount === 0) {
  console.log('\n🎉 Installation verification completed successfully!');
  console.log('🚀 You can now run: npm run dev');
} else {
  console.log('\n⚠️ Some checks failed. Please review the issues above.');
  console.log('💡 Try: npm install && npm run build');
}
