const webpush = require('web-push');

/**
 * Generate VAPID Keys for Push Notifications
 * 
 * VAPID (Voluntary Application Server Identification) keys are required
 * for sending push notifications. This script generates a pair of keys
 * that you'll need to configure in your PWA settings.
 */

console.log('🔑 Generating VAPID Keys for Push Notifications...\n');

try {
  // Generate VAPID key pair
  const vapidKeys = webpush.generateVAPIDKeys();

  console.log('✅ VAPID Keys Generated Successfully!\n');
  
  console.log('📋 Copy these keys to your PWA settings:\n');
  
  console.log('🔓 PUBLIC KEY:');
  console.log('─'.repeat(60));
  console.log(vapidKeys.publicKey);
  console.log('');
  
  console.log('🔐 PRIVATE KEY:');
  console.log('─'.repeat(60));
  console.log(vapidKeys.privateKey);
  console.log('');
  
  console.log('🎯 HOW TO USE THESE KEYS:\n');
  console.log('1. 🔗 Visit your admin panel: http://localhost:3000/admin/pwa');
  console.log('2. 📝 Go to the "PWA Settings" tab');
  console.log('3. 🔧 Update these settings:');
  console.log('   • vapid_public_key: Copy the PUBLIC KEY above');
  console.log('   • vapid_private_key: Copy the PRIVATE KEY above');
  console.log('4. 💾 Save the settings');
  console.log('5. 🧪 Test notifications at: http://localhost:3000/debug-notifications\n');
  
  console.log('⚠️  IMPORTANT SECURITY NOTES:');
  console.log('• Keep the PRIVATE KEY secret - never expose it in client-side code');
  console.log('• The PUBLIC KEY can be safely used in your frontend');
  console.log('• Store these keys securely in your production environment');
  console.log('• Do not commit these keys to version control\n');
  
  console.log('🏁 Next Steps:');
  console.log('1. Configure the keys in your PWA settings');
  console.log('2. Test notifications with the debug tool');
  console.log('3. Create your first notification campaign!');
  
} catch (error) {
  console.error('❌ Error generating VAPID keys:', error.message);
  console.log('\n💡 Make sure you have installed the required dependencies:');
  console.log('npm install web-push');
  process.exit(1);
} 