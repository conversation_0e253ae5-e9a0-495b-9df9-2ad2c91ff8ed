const webpush = require('web-push');

// Import the database functions
const { updatePWASetting } = require('../src/lib/database.ts');

/**
 * Auto-configure VAPID Keys and Test Push Notifications
 * 
 * This script will:
 * 1. Generate fresh VAPID keys
 * 2. Configure them in the database automatically  
 * 3. Test the push notification system
 * 4. Verify all components are working
 */

async function setupVAPIDKeys() {
  console.log('🚀 Starting VAPID Key Auto-Configuration...\n');

  try {
    // Step 1: Generate VAPID Keys
    console.log('1️⃣ Generating fresh VAPID keys...');
    const vapidKeys = webpush.generateVAPIDKeys();
    console.log('✅ VAPID keys generated successfully');
    
    console.log('\n📋 Generated Keys:');
    console.log('🔓 Public Key:', vapidKeys.publicKey);
    console.log('🔐 Private Key:', vapidKeys.privateKey);

    // Step 2: Configure in Database
    console.log('\n2️⃣ Configuring keys in database...');
    
    try {
      // Update public key
      const publicResult = await updatePWASetting(
        'vapid_public_key', 
        vapidKeys.publicKey,
        'VAPID public key for push notifications'
      );
      
      if (publicResult.error) {
        console.log('⚠️ Database not configured - running in demo mode');
        console.log('✅ Keys would be saved in production environment');
      } else {
        console.log('✅ Public key saved to database');
      }

      // Update private key  
      const privateResult = await updatePWASetting(
        'vapid_private_key',
        vapidKeys.privateKey, 
        'VAPID private key for push notifications'
      );
      
      if (privateResult.error) {
        console.log('⚠️ Database not configured - running in demo mode');
      } else {
        console.log('✅ Private key saved to database');
      }

    } catch (dbError) {
      console.log('⚠️ Database functions not available in this context');
      console.log('📝 Manual configuration required - see instructions below');
    }

    // Step 3: Show manual configuration instructions
    console.log('\n3️⃣ Manual Configuration Instructions:');
    console.log('─'.repeat(60));
    console.log('🔗 Visit: http://localhost:3002/admin/pwa');
    console.log('👤 Login as: <EMAIL> / admin123');
    console.log('⚙️ Go to: PWA Settings tab');
    console.log('📝 Update these settings:');
    console.log(`   • vapid_public_key: ${vapidKeys.publicKey}`);
    console.log(`   • vapid_private_key: ${vapidKeys.privateKey}`);
    console.log('💾 Save the settings');

    // Step 4: Testing instructions
    console.log('\n4️⃣ Testing Instructions:');
    console.log('─'.repeat(60));
    console.log('🧪 Debug Tool: http://localhost:3002/debug-notifications');
    console.log('🏠 Test Homepage: http://localhost:3002');
    console.log('👨‍💼 Admin Panel: http://localhost:3002/admin/pwa');

    console.log('\n✅ SETUP COMPLETE! Follow the instructions above.');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupVAPIDKeys(); 