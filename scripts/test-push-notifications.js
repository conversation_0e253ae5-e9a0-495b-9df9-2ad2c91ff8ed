/**
 * Push Notification Test Script
 * 
 * This script tests all aspects of the push notification system
 * to verify that all fixes are working properly.
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3002';

async function testPushNotificationSystem() {
  console.log('🧪 Testing Push Notification System...\n');

  const tests = [];

  // Test 1: Check if debug page is accessible
  try {
    console.log('1️⃣ Testing debug page accessibility...');
    const response = await fetch(`${BASE_URL}/debug-notifications`);
    if (response.ok) {
      tests.push({ name: 'Debug page accessible', status: '✅ PASS' });
      console.log('✅ Debug page is accessible');
    } else {
      tests.push({ name: 'Debug page accessible', status: '❌ FAIL' });
      console.log('❌ Debug page is not accessible');
    }
  } catch (error) {
    tests.push({ name: 'Debug page accessible', status: '❌ FAIL' });
    console.log('❌ Debug page is not accessible:', error.message);
  }

  // Test 2: Check VAPID key API endpoint
  try {
    console.log('\n2️⃣ Testing VAPID key API endpoint...');
    const response = await fetch(`${BASE_URL}/api/pwa/vapid-key`);
    const data = await response.json();
    
    if (response.ok) {
      tests.push({ name: 'VAPID key API working', status: '✅ PASS' });
      console.log('✅ VAPID key API is working');
      
      if (data.configured) {
        tests.push({ name: 'VAPID keys configured', status: '✅ PASS' });
        console.log('✅ VAPID keys are configured');
      } else {
        tests.push({ name: 'VAPID keys configured', status: '⚠️ PENDING' });
        console.log('⚠️ VAPID keys need to be configured');
      }
    } else {
      tests.push({ name: 'VAPID key API working', status: '❌ FAIL' });
      console.log('❌ VAPID key API is not working');
    }
  } catch (error) {
    tests.push({ name: 'VAPID key API working', status: '❌ FAIL' });
    console.log('❌ VAPID key API error:', error.message);
  }

  // Test 3: Check admin panel accessibility
  try {
    console.log('\n3️⃣ Testing admin panel accessibility...');
    const response = await fetch(`${BASE_URL}/admin/pwa`);
    if (response.ok) {
      tests.push({ name: 'Admin panel accessible', status: '✅ PASS' });
      console.log('✅ Admin panel is accessible');
    } else {
      tests.push({ name: 'Admin panel accessible', status: '❌ FAIL' });
      console.log('❌ Admin panel is not accessible');
    }
  } catch (error) {
    tests.push({ name: 'Admin panel accessible', status: '❌ FAIL' });
    console.log('❌ Admin panel error:', error.message);
  }

  // Test 4: Check service worker
  try {
    console.log('\n4️⃣ Testing service worker...');
    const response = await fetch(`${BASE_URL}/sw.js`);
    if (response.ok) {
      tests.push({ name: 'Service worker accessible', status: '✅ PASS' });
      console.log('✅ Service worker is accessible');
    } else {
      tests.push({ name: 'Service worker accessible', status: '❌ FAIL' });
      console.log('❌ Service worker is not accessible');
    }
  } catch (error) {
    tests.push({ name: 'Service worker accessible', status: '❌ FAIL' });
    console.log('❌ Service worker error:', error.message);
  }

  // Test 5: Check homepage
  try {
    console.log('\n5️⃣ Testing homepage...');
    const response = await fetch(`${BASE_URL}/`);
    if (response.ok) {
      tests.push({ name: 'Homepage accessible', status: '✅ PASS' });
      console.log('✅ Homepage is accessible');
    } else {
      tests.push({ name: 'Homepage accessible', status: '❌ FAIL' });
      console.log('❌ Homepage is not accessible');
    }
  } catch (error) {
    tests.push({ name: 'Homepage accessible', status: '❌ FAIL' });
    console.log('❌ Homepage error:', error.message);
  }

  // Test 6: Check push test page
  try {
    console.log('\n6️⃣ Testing push test page...');
    const response = await fetch(`${BASE_URL}/push-test`);
    if (response.ok) {
      tests.push({ name: 'Push test page accessible', status: '✅ PASS' });
      console.log('✅ Push test page is accessible');
    } else {
      tests.push({ name: 'Push test page accessible', status: '❌ FAIL' });
      console.log('❌ Push test page is not accessible');
    }
  } catch (error) {
    tests.push({ name: 'Push test page accessible', status: '❌ FAIL' });
    console.log('❌ Push test page error:', error.message);
  }

  // Test 7: Check PWA status page
  try {
    console.log('\n7️⃣ Testing PWA status page...');
    const response = await fetch(`${BASE_URL}/pwa-status`);
    if (response.ok) {
      tests.push({ name: 'PWA status page accessible', status: '✅ PASS' });
      console.log('✅ PWA status page is accessible');
    } else {
      tests.push({ name: 'PWA status page accessible', status: '❌ FAIL' });
      console.log('❌ PWA status page is not accessible');
    }
  } catch (error) {
    tests.push({ name: 'PWA status page accessible', status: '❌ FAIL' });
    console.log('❌ PWA status page error:', error.message);
  }

  // Summary
  console.log('\n🏁 TEST SUMMARY');
  console.log('─'.repeat(50));
  
  tests.forEach(test => {
    console.log(`${test.status} ${test.name}`);
  });

  const passCount = tests.filter(t => t.status.includes('✅')).length;
  const failCount = tests.filter(t => t.status.includes('❌')).length;
  const pendingCount = tests.filter(t => t.status.includes('⚠️')).length;

  console.log('\n📊 RESULTS:');
  console.log(`✅ Passed: ${passCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log(`⚠️ Pending: ${pendingCount}`);

  if (failCount === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Push notification system is working.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the issues above.');
  }

  // Next steps
  console.log('\n📋 NEXT STEPS:');
  
  if (pendingCount > 0) {
    console.log('1. 🔑 Configure VAPID keys in admin panel:');
    console.log(`   ${BASE_URL}/admin/pwa`);
    console.log('   Login: <EMAIL> / admin123');
  }
  
  console.log('2. 🧪 Test in browser:');
  console.log(`   Debug Tool: ${BASE_URL}/debug-notifications`);
  console.log(`   Homepage: ${BASE_URL}`);
  
  console.log('3. 📱 Test on mobile device for best results');
  
  console.log('\n✅ System is ready for push notifications!');
}

// Run the tests
testPushNotificationSystem(); 