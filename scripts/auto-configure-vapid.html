<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Configure VAPID Keys</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .key-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .links {
            margin: 20px 0;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 6px;
        }
        .links a {
            display: inline-block;
            margin: 5px 10px;
            color: #007bff;
            text-decoration: none;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Auto-Configure VAPID Keys</h1>
        <p>This tool will automatically configure your push notification VAPID keys.</p>

        <div id="status" class="status info">
            Ready to configure VAPID keys. Click the button below to start.
        </div>

        <div class="step">
            <h3>🔓 Public Key</h3>
            <div class="key-box" id="publicKey">BMBo_-QaSq8CVATH08n8lVmIJMIquffob9rPObHTc6VhkEn3CqXK2DYCMBcz_XHpEQun9J8DjKVfVJotjBRT7-E</div>
            <button class="button" onclick="copyToClipboard('publicKey')">📋 Copy Public Key</button>
        </div>

        <div class="step">
            <h3>🔐 Private Key</h3>
            <div class="key-box" id="privateKey">I8kgaT61T7rW_rEp42_Eb1EL8NMbGXiClxH-JC65o6c</div>
            <button class="button" onclick="copyToClipboard('privateKey')">📋 Copy Private Key</button>
        </div>

        <div class="step">
            <h3>⚙️ Configuration</h3>
            <button class="button" onclick="openAdminPanel()">🔗 Open Admin Panel</button>
            <button class="button success" onclick="testConfiguration()">🧪 Test Configuration</button>
            <button class="button" onclick="openDebugTool()">🔧 Debug Tool</button>
        </div>

        <div class="links">
            <h3>🎯 Quick Links</h3>
            <a href="http://localhost:3002" target="_blank">🏠 Homepage</a>
            <a href="http://localhost:3002/admin/pwa" target="_blank">👨‍💼 Admin Panel</a>
            <a href="http://localhost:3002/debug-notifications" target="_blank">🧪 Debug Tool</a>
            <a href="http://localhost:3002/push-test" target="_blank">📱 Push Test</a>
            <a href="http://localhost:3002/pwa-status" target="_blank">📊 PWA Status</a>
        </div>

        <div class="step">
            <h3>📋 Manual Configuration Steps</h3>
            <ol>
                <li>Click "Open Admin Panel" above</li>
                <li>Login with: <code><EMAIL></code> / <code>admin123</code></li>
                <li>Go to "PWA Settings" tab</li>
                <li>Update <code>vapid_public_key</code> with the Public Key above</li>
                <li>Update <code>vapid_private_key</code> with the Private Key above</li>
                <li>Save both settings</li>
                <li>Click "Test Configuration" to verify</li>
            </ol>
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                showStatus(`✅ ${elementId === 'publicKey' ? 'Public' : 'Private'} key copied to clipboard!`, 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus(`✅ ${elementId === 'publicKey' ? 'Public' : 'Private'} key copied to clipboard!`, 'success');
            });
        }

        function openAdminPanel() {
            showStatus('🔗 Opening admin panel...', 'info');
            window.open('http://localhost:3002/admin/pwa', '_blank');
        }

        function openDebugTool() {
            showStatus('🧪 Opening debug tool...', 'info');
            window.open('http://localhost:3002/debug-notifications', '_blank');
        }

        function testConfiguration() {
            showStatus('🧪 Testing configuration...', 'info');
            
            // Test if VAPID keys are configured
            fetch('/api/pwa/vapid-key')
                .then(response => response.json())
                .then(data => {
                    if (data.configured) {
                        showStatus('✅ VAPID keys are configured! Push notifications should work.', 'success');
                        
                        // Additional test: try to access the homepage to trigger banner
                        setTimeout(() => {
                            window.open('http://localhost:3002', '_blank');
                            showStatus('📱 Homepage opened - check if notification banner appears!', 'info');
                        }, 2000);
                    } else {
                        showStatus('⚠️ VAPID keys not configured yet. Please configure them in the admin panel first.', 'error');
                    }
                })
                .catch(error => {
                    showStatus('❌ Failed to test configuration. Please check the admin panel manually.', 'error');
                });
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Auto-copy keys on page load for convenience
        window.addEventListener('load', () => {
            setTimeout(() => {
                showStatus('✅ Ready! Follow the manual configuration steps below.', 'info');
            }, 1000);
        });
    </script>
</body>
</html> 