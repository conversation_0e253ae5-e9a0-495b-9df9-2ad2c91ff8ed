#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createAdminUser() {
  const readline = require('readline')
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })

  const question = (query) => new Promise((resolve) => rl.question(query, resolve))

  try {
    console.log('🔧 Creating Admin User')
    console.log('=====================\n')

    const email = await question('Enter admin email: ')
    const password = await question('Enter admin password: ')
    const name = await question('Enter admin name (optional): ') || 'Admin User'

    console.log('\n⏳ Creating user...')

    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: { name }
    })

    if (authError) {
      console.error('❌ Error creating auth user:', authError.message)
      rl.close()
      return
    }

    console.log('✅ Auth user created successfully')

    // Create user profile in our users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: authData.user.email,
        name: name,
        role: 'admin'
      })
      .select()
      .single()

    if (userError) {
      console.error('❌ Error creating user profile:', userError.message)
      rl.close()
      return
    }

    console.log('✅ User profile created successfully')
    console.log('✅ Admin user created successfully!')
    console.log('\n📋 User Details:')
    console.log(`   ID: ${userData.id}`)
    console.log(`   Email: ${userData.email}`)
    console.log(`   Name: ${userData.name}`)
    console.log(`   Role: ${userData.role}`)
    console.log(`   Created: ${userData.created_at}`)

    rl.close()
  } catch (error) {
    console.error('❌ Unexpected error:', error)
    rl.close()
  }
}

// Run the script
createAdminUser() 