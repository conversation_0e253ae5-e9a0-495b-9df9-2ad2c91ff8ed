{"name": "nextjs-supabase-template", "version": "1.0.0", "description": "Production-ready Next.js 15 template with Supabase, authentication, PWA, and push notifications. Works immediately with demo mode, no dependency conflicts.", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test:template": "node scripts/test-template.js", "setup:vapid": "node scripts/generate-vapid-keys.js", "verify": "node scripts/verify-installation.js", "prepare-release": "node scripts/prepare-release.js"}, "dependencies": {"@next/bundle-analyzer": "^15.0.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@sentry/nextjs": "^8.42.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.0", "@tailwindcss/postcss": "^4.1.11", "@tiptap/core": "^2.10.3", "@tiptap/react": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@types/js-cookie": "^3.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.460.0", "next": "^15.4.4", "next-auth": "5.0.0-beta.25", "react": "^18.3.1", "react-cookie-consent": "^9.0.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "web-push": "^3.6.7"}, "devDependencies": {"@types/node": "^20.17.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/web-push": "^3.6.4", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^8.57.1", "eslint-config-next": "15.0.3", "postcss": "^8.5.0", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}