# 🚀 Deployment Guide - Push Notifications & PWA

This guide covers deploying your template with fully functional push notifications and PWA features.

## ✅ **Pre-Deployment Checklist**

### **1. 🔑 Generate Production VAPID Keys**
```bash
# Generate unique keys for your app
npm run setup:vapid

# Save the keys securely - you'll need them for the admin panel
```

### **2. 🗄️ Database Setup**
```sql
-- Run the complete schema in your Supabase project
-- File: supabase-setup.sql
-- This creates all tables including push_subscriptions, notification_campaigns, etc.
```

### **3. ⚙️ Environment Variables**
```env
# Production .env.local
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your_production_secret_here
```

---

## 🌐 **Platform-Specific Deployment**

### **Vercel (Recommended) ⚡**
```bash
# 1. Deploy to Vercel
npm install -g vercel
vercel

# 2. Add environment variables in Vercel dashboard
# 3. HTTPS is automatic ✅
# 4. Push notifications work immediately!
```

**Post-Deploy Steps:**
1. Visit `https://yourdomain.com/admin/pwa`
2. Login: Create your admin user
3. Add VAPID keys to PWA Settings
4. Test: `https://yourdomain.com/debug-notifications`

### **Netlify 🌟**
```bash
# 1. Build and deploy
npm run build
# Upload dist folder to Netlify

# 2. Configure environment variables in Netlify dashboard
# 3. HTTPS is automatic ✅
# 4. Push notifications work!
```

### **Railway 🚂**
```bash
# 1. Connect GitHub repo
# 2. Railway auto-deploys
# 3. Add environment variables
# 4. HTTPS included ✅
```

### **Custom Server 🖥️**
**Requirements:**
- ✅ **HTTPS Certificate** (Required for push notifications)
- ✅ **Node.js 18+**
- ✅ **PM2 or similar** for process management

```bash
# 1. Build the app
npm run build

# 2. Start with PM2
npm install -g pm2
pm2 start npm --name "your-app" -- start

# 3. Configure nginx with SSL
# 4. Ensure HTTPS works for push notifications
```

---

## 📱 **Push Notifications - Production Setup**

### **1. Configure VAPID Keys**
After deployment:
1. Visit `https://yourdomain.com/admin/pwa`
2. Login with your admin credentials
3. Go to "PWA Settings" tab
4. Add your production VAPID keys:
   - `vapid_public_key`: [Your public key from setup:vapid]
   - `vapid_private_key`: [Your private key from setup:vapid]
5. Save settings

### **2. Test Push Notifications**
```bash
# Production testing checklist:
✅ Visit: https://yourdomain.com/debug-notifications
✅ Click: "Test Banner Conditions" → All green checkmarks
✅ Click: "Test Permission" → Browser prompts for notifications
✅ Click: "Test Push Subscription" → Subscription created
✅ Admin: "Send Test Notification" → Notification delivered
```

### **3. Mobile Testing**
**iOS Safari (16.4+):**
- Requires "Add to Home Screen" first
- Then notifications work in standalone mode

**Android Chrome:**
- Works in browser and PWA mode
- Best mobile experience

### **4. Cross-Device Verification**
- ✅ Desktop: Chrome, Firefox, Safari, Edge
- ✅ Mobile: iOS Safari PWA, Android Chrome
- ✅ Incognito mode works
- ✅ Multiple users can subscribe

---

## 🔒 **Security & Performance**

### **HTTPS Requirements**
```bash
# Push notifications REQUIRE HTTPS in production
# ✅ Vercel, Netlify, Railway: Automatic HTTPS
# ⚠️ Custom servers: Must configure SSL certificate
```

### **Environment Security**
```env
# Keep these SECRET in production:
SUPABASE_SERVICE_ROLE_KEY=secret_key
NEXTAUTH_SECRET=secret_key  
# VAPID private key (stored in database, not env)

# These are PUBLIC (safe to expose):
NEXT_PUBLIC_SUPABASE_URL=public_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=public_key
# VAPID public key (safe to expose to frontend)
```

### **Performance Optimization**
```typescript
// Already included in template:
✅ Service worker caching
✅ Optimistic push subscription updates
✅ Efficient notification batching
✅ Background sync for offline users
✅ PWA asset preloading
```

---

## 📊 **Production Monitoring**

### **Analytics & Tracking**
- ✅ **Delivery rates** - Track in admin panel
- ✅ **Click-through rates** - Monitor user engagement  
- ✅ **Subscription growth** - See user adoption
- ✅ **Failed notifications** - Debug delivery issues

### **Error Monitoring**
```bash
# Built-in Sentry integration:
# 1. Sign up for Sentry
# 2. Add SENTRY_DSN to environment variables
# 3. Automatic error tracking for push notifications
```

### **Database Monitoring**
```sql
-- Monitor push notification performance:
SELECT 
  date_trunc('day', sent_at) as date,
  count(*) as total_sent,
  count(*) filter (where status = 'delivered') as delivered,
  round(count(*) filter (where status = 'delivered') * 100.0 / count(*), 2) as delivery_rate
FROM notifications 
WHERE sent_at > now() - interval '30 days'
GROUP BY date_trunc('day', sent_at)
ORDER BY date;
```

---

## 🚀 **Scaling Considerations**

### **High-Volume Deployments**
```bash
# For 10,000+ users:
✅ Database indexing (already included)
✅ Batch notification sending (built-in)
✅ Rate limiting (configured)
✅ Queue management (implemented)
✅ Error handling (comprehensive)
```

### **Multi-Region Setup**
```bash
# For global apps:
1. Deploy app to multiple regions (Vercel Edge Functions)
2. Use single Supabase database (global)
3. Push notifications work from any region
4. CDN handles static assets
```

---

## ⚠️ **Common Deployment Issues**

### **"Push notifications not working in production"**
**Cause:** Missing HTTPS or VAPID keys not configured

**Solution:**
1. Verify HTTPS: `https://` in URL
2. Check VAPID keys in admin panel
3. Test with debug tool: `/debug-notifications`

### **"No notifications on mobile"**
**Cause:** iOS requires PWA installation

**Solution:**
1. iOS: "Add to Home Screen" first
2. Android: Works in browser
3. Test permissions in system settings

### **"Admin panel shows wrong mode"**
**Cause:** Environment variables not set correctly

**Solution:**
1. Verify all Supabase env vars are set
2. Check NEXTAUTH_SECRET is production value
3. Restart deployment after env changes

---

## 📋 **Post-Deployment Checklist**

```bash
✅ HTTPS working (https://yourdomain.com)
✅ Environment variables configured
✅ Database schema deployed (supabase-setup.sql)
✅ VAPID keys configured in admin panel
✅ Admin user created
✅ Push notifications tested and working
✅ PWA installable (manifest.json accessible)
✅ Service worker registered (/sw.js loads)
✅ Mobile testing completed
✅ Analytics/monitoring configured
✅ Error tracking enabled (Sentry)
```

---

## 🎯 **Success Metrics**

Your deployment is successful when:
- ✅ Push notifications deliver to all devices
- ✅ PWA installs on mobile devices
- ✅ Admin panel manages campaigns effectively
- ✅ Users can subscribe/unsubscribe easily
- ✅ Analytics show engagement metrics
- ✅ No console errors in production

---

**🚀 Your push notification system is now live and ready to engage users across all platforms!**

For ongoing support:
- Monitor admin panel analytics
- Review error logs regularly  
- Test new features in staging first
- Keep dependencies updated 