import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { supabase } from '@/lib/supabase'

// Extend global type for demo subscriptions
declare global {
  var demoSubscriptions: any[] | undefined
}

export async function POST(request: NextRequest) {
  try {
    // Get current session - allow anonymous users for PWA experience
    const session = await auth()
    const userId = session?.user?.id || 'anonymous-' + Date.now()
    const userEmail = session?.user?.email || '<EMAIL>'

    const { subscription, userAgent } = await request.json()

    if (!subscription || !subscription.endpoint) {
      return NextResponse.json({ error: 'Invalid subscription data' }, { status: 400 })
    }

    console.log('📱 Creating push subscription for user:', userEmail)

    // If no Supabase, create demo subscription for testing
    if (!supabase) {
      console.log('✅ Demo mode: Subscription created successfully')
      const demoSubscription = {
        id: 'demo-' + Date.now(),
        user_id: userId,
        endpoint: subscription.endpoint,
        p256dh: subscription.keys?.p256dh || 'demo-p256dh',
        auth: subscription.keys?.auth || 'demo-auth',
        user_agent: userAgent || 'Demo Device',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      // Store real subscription data for demo testing
      // This allows the admin panel to send real notifications in demo mode
      try {
        if (typeof window !== 'undefined') {
          // We're on client side - store in localStorage for demo persistence
          const existingSubs = JSON.parse(localStorage.getItem('demo-real-subscriptions') || '[]')
          existingSubs.push(demoSubscription)
          localStorage.setItem('demo-real-subscriptions', JSON.stringify(existingSubs))
          console.log('📱 Real subscription stored in localStorage for demo testing')
        } else {
          // We're on server side - store in global variable for this session
          global.demoSubscriptions = global.demoSubscriptions || []
          global.demoSubscriptions.push(demoSubscription)
          console.log('📱 Real subscription stored in global for demo testing')
        }
      } catch (error) {
        console.log('Could not store demo subscription, but creation succeeded')
      }
      
      return NextResponse.json({ 
        message: 'Subscription saved successfully (demo mode)',
        subscription: demoSubscription
      })
    }

    // Check if subscription already exists
    const { data: existingSubscription } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('endpoint', subscription.endpoint)
      .single()

    if (existingSubscription) {
      // Update existing subscription to active
      const { data, error } = await supabase
        .from('push_subscriptions')
        .update({ 
          is_active: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSubscription.id)
        .select()
        .single()

      if (error) {
        console.error('❌ Failed to update subscription:', error)
        return NextResponse.json({ error: 'Failed to update subscription' }, { status: 500 })
      }

      console.log('✅ Push subscription updated successfully:', data.id)
      return NextResponse.json({
        message: 'Subscription updated successfully',
        subscription: data
      })
    }

    // Create new subscription
    const { data, error } = await supabase
      .from('push_subscriptions')
      .insert({
        user_id: userId,
        endpoint: subscription.endpoint,
        p256dh: subscription.keys?.p256dh || '',
        auth: subscription.keys?.auth || '',
        user_agent: userAgent || 'Unknown Device',
        is_active: true
      })
      .select()
      .single()

    if (error) {
      console.error('❌ Failed to save subscription:', error)
      return NextResponse.json({ error: 'Failed to save subscription' }, { status: 500 })
    }

    console.log('✅ Push subscription saved successfully:', data.id)

    return NextResponse.json({
      message: 'Subscription saved successfully',
      subscription: data
    })

  } catch (error) {
    console.error('💥 Push subscription error:', error)
    return NextResponse.json({ 
      error: 'Failed to process subscription',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()
    const userId = session?.user?.id || 'anonymous'
    const userEmail = session?.user?.email || '<EMAIL>'

    const { endpoint } = await request.json()

    console.log('🗑️ Unsubscribing user:', userEmail)

    if (!supabase) {
      console.log('✅ Demo mode: Unsubscribed successfully')
      return NextResponse.json({ message: 'Unsubscribed successfully (demo mode)' })
    }

    // Deactivate subscription
    const { error } = await supabase
      .from('push_subscriptions')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('endpoint', endpoint)

    if (error) {
      console.error('❌ Failed to unsubscribe:', error)
      return NextResponse.json({ error: 'Failed to unsubscribe' }, { status: 500 })
    }

    console.log('✅ Unsubscribed successfully')
    return NextResponse.json({ message: 'Unsubscribed successfully' })
  } catch (error) {
    console.error('💥 Unsubscribe error:', error)
    return NextResponse.json({ 
      error: 'Failed to unsubscribe',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
} 