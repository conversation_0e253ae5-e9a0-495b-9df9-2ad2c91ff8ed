import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { 
  getAllPushSubscriptions, 
  getAllUsers,
  getNotificationCampaign,
  updateNotificationCampaign,
  getPWASetting
} from '@/lib/database'
import { supabase } from '@/lib/supabase'
import webpush from 'web-push'

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await auth()
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { campaignId } = await request.json()

    if (!campaignId) {
      return NextResponse.json({ error: 'Campaign ID is required' }, { status: 400 })
    }

    // Get campaign details
    const { data: campaign, error: campaignError } = await getNotificationCampaign(campaignId)
    if (campaignError || !campaign) {
      return NextResponse.json({ error: 'Campaign not found' }, { status: 404 })
    }

    if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
      return NextResponse.json({ error: 'Campaign already sent or in progress' }, { status: 400 })
    }

    // Get VAPID keys from settings
    const { data: publicKey } = await getPWASetting('vapid_public_key')
    const { data: privateKey } = await getPWASetting('vapid_private_key')

    if (!publicKey?.value || !privateKey?.value || publicKey.value === '""' || privateKey.value === '""') {
      return NextResponse.json({ 
        error: 'VAPID keys not configured. Please set up push notification credentials in PWA settings.' 
      }, { status: 400 })
    }

    // Configure web-push
    webpush.setVapidDetails(
      'mailto:<EMAIL>', // Replace with your email
      publicKey.value.replace(/"/g, ''),
      privateKey.value.replace(/"/g, '')
    )

    // Update campaign status to sending
    await updateNotificationCampaign(campaignId, { 
      status: 'sending',
      sent_at: new Date().toISOString()
    })

    // Get target users based on campaign criteria
    let targetUserIds: string[] = []
    
    if (campaign.target_type === 'all') {
      const { data: users } = await getAllUsers()
      targetUserIds = users?.map(user => user.id) || []
    } else if (campaign.target_type === 'role') {
      const { data: users } = await getAllUsers()
      const targetRole = campaign.target_criteria?.role
      targetUserIds = users?.filter(user => user.role === targetRole).map(user => user.id) || []
    } else if (campaign.target_type === 'specific') {
      targetUserIds = campaign.target_criteria?.userIds || []
    } else if (campaign.target_type === 'active') {
      // Get users who have been active in the last 30 days (customize this logic)
      const { data: users } = await getAllUsers()
      targetUserIds = users?.map(user => user.id) || []
    }

    if (targetUserIds.length === 0) {
      await updateNotificationCampaign(campaignId, { 
        status: 'cancelled',
        total_sent: 0
      })
      return NextResponse.json({ error: 'No target users found' }, { status: 400 })
    }

    // Get push subscriptions for target users
    const { data: allSubscriptions } = await getAllPushSubscriptions()
    const targetSubscriptions = allSubscriptions?.filter(
      sub => targetUserIds.includes(sub.user_id) && sub.is_active
    ) || []

    if (targetSubscriptions.length === 0) {
      await updateNotificationCampaign(campaignId, { 
        status: 'cancelled',
        total_sent: 0
      })
      return NextResponse.json({ 
        error: 'No active push subscriptions found for target users' 
      }, { status: 400 })
    }

    // Update campaign with recipient count
    await updateNotificationCampaign(campaignId, { 
      status: 'sending'
    })

    const notification = {
      title: campaign.title,
      body: campaign.body,
      icon: campaign.icon || '/icons/icon-192x192.png',
      badge: campaign.badge || '/icons/icon-72x72.png',
      image: campaign.image,
      url: campaign.url,
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        campaignId: campaign.id,
        primaryKey: campaign.id
      },
      actions: campaign.actions || [
        {
          action: 'explore',
          title: 'View',
          icon: '/icons/icon-192x192.png'
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/icons/icon-192x192.png'
        }
      ]
    }

    // Send notifications and create notification records
    const results = await Promise.allSettled(
      targetSubscriptions.map(async (subscription) => {
        const pushSubscription = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: subscription.p256dh,
            auth: subscription.auth
          }
        }

        try {
          // Send the push notification
          await webpush.sendNotification(pushSubscription, JSON.stringify(notification))

          // Create notification record in database
          if (supabase) {
            await supabase.from('notifications').insert({
              campaign_id: campaign.id,
              user_id: subscription.user_id,
              subscription_id: subscription.id,
              title: campaign.title,
              body: campaign.body,
              icon: campaign.icon,
              badge: campaign.badge,
              image: campaign.image,
              url: campaign.url,
              actions: campaign.actions,
              payload: notification,
              status: 'sent',
              sent_at: new Date().toISOString()
            })
          }

          return { success: true, subscriptionId: subscription.id }
        } catch (error) {
          console.error('Failed to send notification:', error)

          // Create failed notification record
          if (supabase) {
            await supabase.from('notifications').insert({
              campaign_id: campaign.id,
              user_id: subscription.user_id,
              subscription_id: subscription.id,
              title: campaign.title,
              body: campaign.body,
              status: 'failed',
              error_message: error instanceof Error ? error.message : String(error)
            })
          }

          return { success: false, subscriptionId: subscription.id, error: error instanceof Error ? error.message : String(error) }
        }
      })
    )

    const successful = results.filter(result => result.status === 'fulfilled' && result.value.success).length
    const failed = results.length - successful

    // Update final campaign stats
    await updateNotificationCampaign(campaignId, { 
      status: 'sent',
      total_sent: successful,
      total_delivered: successful, // Will be updated by webhook/tracking
      total_clicked: 0 // Will be updated by click tracking
    })

    return NextResponse.json({
      message: 'Campaign sent successfully',
      campaignId: campaign.id,
      stats: {
        total: results.length,
        successful,
        failed,
        recipients: targetSubscriptions.length
      }
    })

  } catch (error) {
    console.error('Campaign sending error:', error)
    return NextResponse.json({ 
      error: 'Failed to send campaign' 
    }, { status: 500 })
  }
} 