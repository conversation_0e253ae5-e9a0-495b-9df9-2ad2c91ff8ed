import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { getAllPushSubscriptions, createNotificationCampaign, getPWASetting } from '@/lib/database'
import webpush from 'web-push'

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await auth()
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { title, body, icon, url } = await request.json()

    if (!title || !body) {
      return NextResponse.json({ error: 'Title and body are required' }, { status: 400 })
    }

    // Get VAPID keys from settings
    const { data: publicKey } = await getPWASetting('vapid_public_key')
    const { data: privateKey } = await getPWASetting('vapid_private_key')

    if (!publicKey?.value || !privateKey?.value || publicKey.value === '""' || privateKey.value === '""') {
      return NextResponse.json({ 
        error: 'VAPID keys not configured. Please set up push notification credentials in PWA settings.' 
      }, { status: 400 })
    }

    // Configure web-push
    webpush.setVapidDetails(
      'mailto:<EMAIL>', // Replace with your email
      publicKey.value.replace(/"/g, ''),
      privateKey.value.replace(/"/g, '')
    )

    // Get all active push subscriptions
    const { data: subscriptions, error } = await getAllPushSubscriptions()
    if (error || !subscriptions?.length) {
      return NextResponse.json({ 
        error: 'No active push subscriptions found' 
      }, { status: 400 })
    }

    const notification = {
      title,
      body,
      icon: icon || '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      url: url || '/',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: 1
      },
      actions: [
        {
          action: 'explore',
          title: 'View',
          icon: '/icons/icon-192x192.png'
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/icons/icon-192x192.png'
        }
      ]
    }

    // Send notifications to all subscriptions
    const results = await Promise.allSettled(
      subscriptions.map(async (subscription) => {
        const pushSubscription = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: subscription.p256dh,
            auth: subscription.auth
          }
        }

        try {
          await webpush.sendNotification(pushSubscription, JSON.stringify(notification))
          return { success: true, subscriptionId: subscription.id }
        } catch (error) {
          console.error('Failed to send notification:', error)
          return { success: false, subscriptionId: subscription.id, error: error instanceof Error ? error.message : String(error) }
        }
      })
    )

    const successful = results.filter(result => result.status === 'fulfilled' && result.value.success).length
    const failed = results.length - successful

    return NextResponse.json({
      message: 'Test notification sent',
      stats: {
        total: results.length,
        successful,
        failed
      }
    })

  } catch (error) {
    console.error('Test notification error:', error)
    return NextResponse.json({ 
      error: 'Failed to send test notification' 
    }, { status: 500 })
  }
} 