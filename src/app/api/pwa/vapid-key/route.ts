import { NextResponse } from 'next/server'
import { getPWASetting } from '@/lib/database'

export async function GET() {
  try {
    // Get the public VAPID key from settings
    const { data: publicKeyData, error } = await getPWASetting('vapid_public_key')
    
    if (error) {
      console.error('Failed to get VAPID public key:', error)
      return NextResponse.json({ 
        error: 'Failed to retrieve VAPID key',
        publicKey: null 
      }, { status: 500 })
    }

    // Return the public key (safe to expose to frontend)
    const publicKey = publicKeyData?.value || ''
    
    return NextResponse.json({ 
      publicKey: publicKey === '""' ? null : publicKey.replace(/"/g, ''),
      configured: publicKey !== '""' && publicKey !== ''
    })
  } catch (error) {
    console.error('VAPID key API error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      publicKey: null 
    }, { status: 500 })
  }
} 