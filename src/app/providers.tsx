'use client';

import { SessionProvider } from 'next-auth/react';
import PWAProvider from './pwa-provider';
import GoogleAnalytics from '@/components/GoogleAnalytics';
import CookieConsentBanner from '@/components/CookieConsentBanner';
// Notification components removed - PWA handles notifications cleanly

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <PWAProvider>
        {children}
        {/* Clean website - no notification banners */}
        <GoogleAnalytics />
        <CookieConsentBanner />
      </PWAProvider>
    </SessionProvider>
  );
} 