'use client'

export default function OfflinePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center p-8">
        <div className="text-6xl mb-8">📱</div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          You&apos;re Offline
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          It looks like you&apos;re not connected to the internet. Don&apos;t worry, you can still browse previously visited pages.
        </p>
        
        <div className="space-y-4">
          <button 
            onClick={() => window.history.back()}
            className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            ← Go Back
          </button>
          
          <button 
            onClick={() => window.location.reload()}
            className="w-full bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            🔄 Try Again
          </button>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>Cached pages available:</p>
          <div className="mt-2 space-y-1">
            <button onClick={() => window.location.href = '/'} className="block text-blue-600 hover:underline">Home</button>
            <a href="/dashboard" className="block text-blue-600 hover:underline">Dashboard</a>
            <a href="/auth/signin" className="block text-blue-600 hover:underline">Sign In</a>
          </div>
        </div>
      </div>
    </div>
  );
} 