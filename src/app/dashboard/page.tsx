'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { ConfigStatus } from '@/components/ConfigStatus'

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    setIsLoading(false)
  }, [session, status, router])

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Configuration Status */}
        <ConfigStatus />
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-2 text-gray-600">
            Welcome back, {session.user.name || session.user.email}!
          </p>
          <p className="text-sm text-gray-500">Role: {session.user.role}</p>
        </div>

        {/* Dashboard Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* User Info Card */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                  <span className="text-white font-medium">
                    {session.user.name ? session.user.name.charAt(0).toUpperCase() : 'U'}
                  </span>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">Profile</h3>
                <p className="text-sm text-gray-500">Manage your account</p>
              </div>
            </div>
            <div className="mt-4">
              <a
                href="/profile"
                className="text-blue-600 hover:text-blue-500 text-sm font-medium"
              >
                View Profile →
              </a>
            </div>
          </div>

          {/* Quick Actions Card */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                  <span className="text-white font-medium">⚡</span>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
                <p className="text-sm text-gray-500">Common tasks</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="space-y-2">
                <button className="w-full text-left text-sm text-blue-600 hover:text-blue-500">
                  Update Settings
                </button>
                <button className="w-full text-left text-sm text-blue-600 hover:text-blue-500">
                  View Activity
                </button>
              </div>
            </div>
          </div>

          {/* Admin Access Card (if admin) */}
          {session.user.role === 'admin' && (
            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
                    <span className="text-white font-medium">⚙️</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">Admin Panel</h3>
                  <p className="text-sm text-gray-500">System management</p>
                </div>
              </div>
              <div className="mt-4">
                <a
                  href="/admin"
                  className="text-purple-600 hover:text-purple-500 text-sm font-medium"
                >
                  Open Admin Panel →
                </a>
              </div>
            </div>
          )}
        </div>

        {/* Welcome Message */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Getting Started</h2>
          <div className="prose text-gray-600">
            <p className="mb-4">
              This is your user dashboard. This template provides a clean foundation for building real applications with:
            </p>
            <ul className="list-disc list-inside space-y-1 mb-4">
              <li>User authentication and role-based access control</li>
              <li>Supabase database integration</li>
              <li>Modern UI components with Tailwind CSS</li>
              <li>Admin panel for user management</li>
              <li>Progressive Web App (PWA) capabilities</li>
            </ul>
            <p>
              Start building your application by customizing this dashboard and adding your specific features.
            </p>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 flex justify-center space-x-4">
          {session.user.role === 'admin' && (
            <a
              href="/admin"
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              Admin Panel
            </a>
          )}
          <a
            href="/profile"
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Profile
          </a>
          <a
            href="/"
            className="bg-gray-600 text-white px-4 py-2 rounded-md hover:gray-700 transition-colors"
          >
            Back to Home
          </a>
        </div>
      </div>
    </div>
  )
} 