import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Providers } from './providers';
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  minimumScale: 1,
  shrinkToFit: 'no',
  userScalable: false,
  viewportFit: 'cover',
  themeColor: '#2563eb',
};

export const metadata: Metadata = {
  title: "Next.js + Supabase Advanced Template",
  description: "A production-ready Next.js 15 template featuring shadcn/ui components, Supabase backend, authentication, PWA support, and modern developer tooling.",
  
  // PWA metadata
  manifest: "/manifest.json",
  keywords: ["nextjs", "supabase", "pwa", "template", "authentication", "dashboard"],
  authors: [{ name: "Next.js Template" }],
  creator: "Next.js + Supabase Template",
  publisher: "Next.js + Supabase Template",
  
  // Apple specific
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "NextJS Template",
  },
  
  // Open Graph
  openGraph: {
    type: "website",
    siteName: "Next.js + Supabase Advanced Template",
    title: "Next.js + Supabase Advanced Template",
    description: "A production-ready Next.js 15 template featuring shadcn/ui components, Supabase backend, authentication, PWA support, and modern developer tooling.",
  },
  
  // Additional PWA metadata
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'NextJS Template',
    'application-name': 'NextJS Template',
    'msapplication-TileColor': '#2563eb',
    'msapplication-tap-highlight': 'no',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* PWA Icons */}
        <link rel="icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        
        {/* Theme color for different browsers */}
        <meta name="theme-color" content="#2563eb" />
        <meta name="msapplication-TileColor" content="#2563eb" />
        
        {/* Prevent zoom on iOS */}
        <meta name="format-detection" content="telephone=no" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
