import Link from 'next/link'
import { ConfigStatus } from '@/components/ConfigStatus'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Configuration Status */}
        <ConfigStatus />
        
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Next.js + Supabase
            <span className="text-blue-600"> Template</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            A production-ready template featuring authentication, role-based access control, 
            database integration, and modern developer tooling. Perfect foundation for building real applications.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/auth/signin" 
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Try Demo Login
            </Link>
            <Link 
              href="/dashboard" 
              className="bg-gray-100 text-gray-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors"
            >
              View Dashboard
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-2xl mb-4">🔐</div>
            <h3 className="text-xl font-semibold mb-2">Authentication & RBAC</h3>
            <p className="text-gray-600 mb-4">
              NextAuth.js with Supabase backend, role-based access control with user, moderator, and admin roles.
            </p>
            <Link 
              href="/auth/signin" 
              className="inline-block bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Authentication
            </Link>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-2xl mb-4">⚙️</div>
            <h3 className="text-xl font-semibold mb-2">Admin Panel</h3>
            <p className="text-gray-600 mb-4">
              Built-in dashboard for user management, settings configuration, and system administration.
            </p>
            <Link 
              href="/admin" 
              className="inline-block bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              Admin Dashboard
            </Link>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-2xl mb-4">🗄️</div>
            <h3 className="text-xl font-semibold mb-2">Database Integration</h3>
            <p className="text-gray-600 mb-4">
              Supabase for real-time data, Row Level Security, and live updates with PostgreSQL.
            </p>
            <div className="text-sm text-gray-500">
              Check out the database schema in <code className="bg-gray-100 px-1 rounded">supabase-setup.sql</code>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-2xl mb-4">⚡</div>
            <h3 className="text-xl font-semibold mb-2">Real-time Features</h3>
            <p className="text-gray-600 mb-4">
              Live updates with Supabase subscriptions, real-time user management, and instant data synchronization.
            </p>
            <div className="text-sm text-gray-500">
              Built-in real-time subscriptions for dynamic updates
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-2xl mb-4">🎨</div>
            <h3 className="text-xl font-semibold mb-2">Modern UI</h3>
            <p className="text-gray-600 mb-4">
              Beautiful, responsive design with shadcn/ui components, Tailwind CSS, and dark mode ready.
            </p>
            <div className="text-sm text-gray-500">
              Professional UI components with accessibility built-in
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-2xl mb-4">🛠️</div>
            <h3 className="text-xl font-semibold mb-2">Developer Experience</h3>
            <p className="text-gray-600 mb-4">
              TypeScript throughout, ESLint configuration, proper project structure, and comprehensive error handling.
            </p>
            <div className="text-sm text-gray-500">
              Production-ready architecture and best practices
            </div>
          </div>
        </div>

        {/* Demo Credentials Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-12">
          <h2 className="text-2xl font-bold text-blue-900 mb-4">🎭 Demo Credentials</h2>
          <p className="text-blue-800 mb-4">
            Try the template immediately with these demo accounts (no setup required):
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-md border border-blue-200">
              <h3 className="font-semibold text-blue-900">👑 Admin Demo</h3>
              <p className="text-sm text-blue-700">Email: <EMAIL></p>
              <p className="text-sm text-blue-700">Password: admin123</p>
            </div>
            <div className="bg-white p-4 rounded-md border border-blue-200">
              <h3 className="font-semibold text-blue-900">👤 User Demo</h3>
              <p className="text-sm text-blue-700">Email: <EMAIL></p>
              <p className="text-sm text-blue-700">Password: user123</p>
            </div>
            <div className="bg-white p-4 rounded-md border border-blue-200">
              <h3 className="font-semibold text-blue-900">🛡️ Moderator Demo</h3>
              <p className="text-sm text-blue-700">Email: <EMAIL></p>
              <p className="text-sm text-blue-700">Password: mod123</p>
            </div>
          </div>
        </div>

        {/* Quick Start Section */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold mb-6">Quick Start</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">1. Environment Setup</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <p className="text-sm text-gray-700 mb-2">Copy environment variables:</p>
                <code className="text-xs bg-gray-100 p-2 rounded block">
                  cp env.example .env.local
                </code>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">2. Database Setup</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <p className="text-sm text-gray-700 mb-2">Run the SQL schema:</p>
                <code className="text-xs bg-gray-100 p-2 rounded block">
                  # Execute supabase-setup.sql in your Supabase project
                </code>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">3. Install Dependencies</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <code className="text-xs bg-gray-100 p-2 rounded block">
                  npm install
                </code>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">4. Create Admin User</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <code className="text-xs bg-gray-100 p-2 rounded block">
                  node scripts/create-admin.js
                </code>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-16 text-center text-gray-500">
          <p>Built with Next.js 15, Supabase, TypeScript, and Tailwind CSS</p>
          <p className="mt-2">Perfect foundation for your next project</p>
        </div>
      </div>
    </div>
  )
}
