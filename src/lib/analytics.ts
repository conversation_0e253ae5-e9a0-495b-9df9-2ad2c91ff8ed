import Cookies from "js-cookie";

export function trackEvent(action: string, category: string, label?: string, value?: number) {
  if (Cookies.get("cookieConsent") !== "true" || !((window as any).gtag)) return;

  (window as any).gtag("event", action, {
    event_category: category,
    event_label: label,
    value,
  });
}

export function trackConversion(action: string, value?: number) {
  trackEvent(action, "conversion", undefined, value);
} 