import NextAuth from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import GitHubProvider from 'next-auth/providers/github'
import CredentialsProvider from 'next-auth/providers/credentials'
import { supabase, isSupabaseReady } from './supabase'

// Check if environment variables are properly configured
const isGoogleConfigured = Boolean(
  process.env.GOOGLE_CLIENT_ID && 
  process.env.GOOGLE_CLIENT_SECRET &&
  process.env.GOOGLE_CLIENT_ID !== 'your_google_client_id' &&
  process.env.GOOGLE_CLIENT_SECRET !== 'your_google_client_secret'
)

const isGitHubConfigured = Boolean(
  process.env.GITHUB_CLIENT_ID && 
  process.env.GITHUB_CLIENT_SECRET &&
  process.env.GITHUB_CLIENT_ID !== 'your_github_client_id' &&
  process.env.GITHUB_CLIENT_SECRET !== 'your_github_client_secret'
)

const isNextAuthConfigured = Boolean(
  process.env.NEXTAUTH_SECRET &&
  process.env.NEXTAUTH_SECRET !== 'your_nextauth_secret_key_here'
)

// Export helper functions for checking configuration status
export const getAuthStatus = () => {
  // Ensure this works on both server and client
  if (typeof window !== 'undefined') {
    // On client side, some env vars might not be available
    return {
      isSupabaseConfigured: Boolean(
        typeof process !== 'undefined' && 
        process.env &&
        process.env.NEXT_PUBLIC_SUPABASE_URL && 
        process.env.NEXT_PUBLIC_SUPABASE_URL !== 'your_supabase_project_url'
      ),
      isGoogleConfigured: false, // OAuth secrets not available on client
      isGitHubConfigured: false, // OAuth secrets not available on client
      isNextAuthConfigured: true, // Assume configured if app is running
      hasAnyProvider: true
    }
  }
  
  // Server side - full check
  return {
    isSupabaseConfigured: isSupabaseReady(),
    isGoogleConfigured,
    isGitHubConfigured,
    isNextAuthConfigured,
    hasAnyProvider: isSupabaseReady() || isGoogleConfigured || isGitHubConfigured
  }
}

// Type declarations for NextAuth
declare module 'next-auth' {
  interface User {
    role: string
  }
  
  interface Session {
    user: {
      id: string
      email: string
      name: string
      image?: string
      role: string
    }
  }
}

// NextAuth v5 configuration
export const { auth, handlers, signIn, signOut } = NextAuth({
  providers: [
    // Only include providers if they're properly configured
    ...(isGoogleConfigured ? [GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    })] : []),
    ...(isGitHubConfigured ? [GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    })] : []),
    // Always include credentials provider for demo mode
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials: any) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Demo users - work without Supabase setup
        const demoUsers = [
          {
            id: 'demo-admin-123',
            email: '<EMAIL>',
            password: 'admin123',
            name: 'Demo Admin',
            role: 'admin'
          },
          {
            id: 'demo-user-456',
            email: '<EMAIL>', 
            password: 'user123',
            name: 'Demo User',
            role: 'user'
          },
          {
            id: 'demo-mod-789',
            email: '<EMAIL>',
            password: 'mod123', 
            name: 'Demo Moderator',
            role: 'moderator'
          }
        ]

        // Check demo users first
        const demoUser = demoUsers.find(user => 
          user.email === credentials.email && user.password === credentials.password
        )
        
        if (demoUser) {
          return {
            id: demoUser.id,
            email: demoUser.email,
            name: demoUser.name,
            role: demoUser.role,
          }
        }

        // If Supabase is configured, try real authentication
        if (isSupabaseReady() && supabase) {
          try {
            const { data, error } = await supabase.auth.signInWithPassword({
              email: credentials.email,
              password: credentials.password,
            })

            if (error || !data.user) {
              return null
            }

            // Get user profile from our users table
            const { data: userProfile } = await supabase
              .from('users')
              .select('*')
              .eq('id', data.user.id)
              .single()

            return {
              id: data.user.id,
              email: data.user.email!,
              name: userProfile?.name || data.user.user_metadata?.name || '',
              role: userProfile?.role || 'user',
            }
          } catch (error) {
            console.error('Auth error:', error)
            return null
          }
        }

        return null
      }
    }),
  ],
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt' as const,
  },
  callbacks: {
    async jwt({ token, user, account }: any) {
      if (user) {
        token.role = user.role || 'user'
      }
      return token
    },
    async session({ session, token }: any) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    },
    async signIn({ user, account, profile }: any) {
      // If Supabase isn't configured, allow sign-in for demo purposes
      if (!isSupabaseReady()) {
        return true
      }

      try {
        if (account?.provider === 'google' || account?.provider === 'github') {
          // Check if user exists in our users table
          const { data: existingUser } = await supabase!
            .from('users')
            .select('*')
            .eq('email', user.email!)
            .single()

          if (!existingUser) {
            // Create new user record
            await supabase!
              .from('users')
              .insert({
                id: user.id,
                email: user.email!,
                name: user.name || '',
                avatar_url: user.image || '',
                role: 'user'
              })
          }
        }
        return true
      } catch (error) {
        console.error('Sign-in error:', error)
        return true // Allow sign-in even if database operations fail
      }
    },
  },
  // Use NextAuth secret if configured, otherwise use a default for demo
  secret: isNextAuthConfigured
    ? process.env.NEXTAUTH_SECRET
    : 'demo-secret-for-template-preview-only',

  // Better URL handling for ngrok and different networks
  trustHost: true,
}) 