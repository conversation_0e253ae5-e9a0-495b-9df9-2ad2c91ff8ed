import { createClient } from '@supabase/supabase-js'
import { supabase, isSupabaseReady, demoUser } from './supabase'
import type { Database } from './supabase'

// Type definitions
export type User = Database['public']['Tables']['users']['Row']
export type UserInsert = Database['public']['Tables']['users']['Insert']
export type UserUpdate = Database['public']['Tables']['users']['Update']

export type Setting = Database['public']['Tables']['settings']['Row']
export type SettingInsert = Database['public']['Tables']['settings']['Insert']
export type SettingUpdate = Database['public']['Tables']['settings']['Update']

// Helper function for when Supabase is not configured
function createNotConfiguredError() {
  return { 
    data: null, 
    error: { message: 'Supabase is not configured. Using demo mode.' } 
  }
}

// User management functions
export async function getCurrentUser() {
  if (!supabase || !isSupabaseReady()) {
    // Return demo user for template preview
    return demoUser
  }

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) return null

  const { data: userProfile } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single()

  return userProfile
}

export async function getUserById(userId: string) {
  if (!supabase || !isSupabaseReady()) {
    // Return demo user for template preview
    return { 
      data: userId === demoUser.id ? demoUser : null, 
      error: null 
    }
  }

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()

  return { data, error }
}

export async function getAllUsers() {
  if (!supabase || !isSupabaseReady()) {
    // Return demo users for template preview
    return { 
      data: [demoUser], 
      error: null 
    }
  }

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .order('created_at', { ascending: false })

  return { data, error }
}

export async function updateUserRole(userId: string, role: 'user' | 'admin' | 'moderator') {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('users')
    .update({ role })
    .eq('id', userId)
    .select()
    .single()

  return { data, error }
}

export async function deleteUser(userId: string) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('users')
    .delete()
    .eq('id', userId)

  return { data, error }
}

// Settings management functions
export async function getSetting(key: string) {
  if (!supabase || !isSupabaseReady()) {
    // Return demo setting for template preview
    const demoSettings: Record<string, any> = {
      site_name: 'Demo Template',
      maintenance_mode: false
    }
    return { 
      data: { key, value: demoSettings[key] || null, description: 'Demo setting' }, 
      error: null 
    }
  }

  const { data, error } = await supabase
    .from('settings')
    .select('*')
    .eq('key', key)
    .single()

  return { data, error }
}

export async function getSettings() {
  if (!supabase || !isSupabaseReady()) {
    // Return demo settings for template preview
    return { 
      data: [
        { key: 'site_name', value: 'Demo Template', description: 'Site name' },
        { key: 'maintenance_mode', value: false, description: 'Maintenance mode' }
      ], 
      error: null 
    }
  }

  const { data, error } = await supabase
    .from('settings')
    .select('*')
    .order('key')

  return { data, error }
}

export async function updateSetting(key: string, value: any, description?: string) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('settings')
    .upsert({ key, value, description })
    .select()
    .single()

  return { data, error }
}

export async function deleteSetting(key: string) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('settings')
    .delete()
    .eq('key', key)

  return { data, error }
}

// Statistics functions
export async function getUserStats() {
  if (!supabase || !isSupabaseReady()) {
    // Return demo stats for template preview
    return { 
      data: {
        total_users: 1,
        new_users_today: 0,
        active_users: 1
      }, 
      error: null 
    }
  }

  try {
    // Get total users
    const { count: totalUsers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })

    // Get users created today
    const today = new Date().toISOString().split('T')[0]
    const { count: newUsersToday } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', today)

    return {
      data: {
        total_users: totalUsers || 0,
        new_users_today: newUsersToday || 0,
        active_users: totalUsers || 0 // Simplified for demo
      },
      error: null
    }
  } catch (error) {
    return { data: null, error }
  }
}

export async function getUserCount() {
  if (!supabase || !isSupabaseReady()) {
    // Return demo count for template preview
    return { count: 1, error: null }
  }

  const { count, error } = await supabase
    .from('users')
    .select('*', { count: 'exact', head: true })

  return { count: count || 0, error }
}

// Profile management functions
export async function getUserProfile(userId: string) {
  if (!supabase || !isSupabaseReady()) {
    // Return demo profile for template preview
    return { 
      data: {
        id: userId,
        full_name: 'Demo User',
        avatar_url: null,
        website: '',
        bio: 'Demo user profile',
        email_preferences: {}
      }, 
      error: null 
    }
  }

  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('user_id', userId)
    .single()

  return { data, error }
}

export async function updateProfile(userId: string, updates: any) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('profiles')
    .upsert({ user_id: userId, ...updates })
    .select()
    .single()

  return { data, error }
}

// Server-side Supabase client creation
export function createServerSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey || 
      supabaseUrl === 'your_supabase_project_url' ||
      supabaseServiceKey === 'your_supabase_service_role_key') {
    return null
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Create admin user function (for server-side use)
export async function createAdminUser(email: string, password: string, name: string) {
  const serverClient = createServerSupabaseClient()
  
  if (!serverClient) {
    return createNotConfiguredError()
  }

  try {
    // Create auth user
    const { data: authUser, error: authError } = await serverClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    })

    if (authError) return { data: null, error: authError }

    // Create user profile
    const { data: userProfile, error: profileError } = await serverClient
      .from('users')
      .insert({
        id: authUser.user.id,
        email,
        name,
        role: 'admin'
      })
      .select()
      .single()

    if (profileError) return { data: null, error: profileError }

    return { data: userProfile, error: null }
  } catch (error) {
    return { data: null, error }
  }
}

// Audit Logs
export async function getAuditLogs(limit: number = 50) {
  if (!supabase || !isSupabaseReady()) {
    // Return demo audit logs for template preview
    return { 
      data: [
        {
          id: '1',
          action: 'CREATE',
          table_name: 'users',
          record_id: '1',
          old_values: null,
          new_values: { name: 'Demo User', email: '<EMAIL>' },
          user_id: demoUser.id,
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          action: 'UPDATE',
          table_name: 'users',
          record_id: demoUser.id,
          old_values: { name: 'Old Name' },
          new_values: { name: demoUser.name },
          user_id: demoUser.id,
          created_at: new Date(Date.now() - 3600000).toISOString()
        }
      ], 
      error: null 
    }
  }

  const { data, error } = await supabase
    .from('audit_logs')
    .select(`
      *,
      user:users(name, email)
    `)
    .order('created_at', { ascending: false })
    .limit(limit)

  return { data, error }
}

// Subscribe to real-time changes
export function subscribeToUserChanges(callback: (payload: any) => void) {
  if (!supabase || !isSupabaseReady()) {
    // Return a dummy subscription for demo mode
    return {
      unsubscribe: () => {}
    }
  }

  return supabase
    .channel('user_changes')
    .on('postgres_changes', { event: '*', schema: 'public', table: 'users' }, callback)
    .subscribe()
}

export function subscribeToAuditLogs(callback: (payload: any) => void) {
  if (!supabase || !isSupabaseReady()) {
    // Return a dummy subscription for demo mode
    return {
      unsubscribe: () => {}
    }
  }

  return supabase
    .channel('audit_logs')
    .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'audit_logs' }, callback)
    .subscribe()
} 

// PWA Management Types and Functions
export interface PWASetting {
  id: string
  key: string
  value: string | boolean | number
  description: string | null
  created_at: string
  updated_at: string
}

export interface PushSubscription {
  id: string
  user_id: string | null
  endpoint: string
  p256dh: string
  auth: string
  user_agent: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface NotificationCampaign {
  id: string
  name: string
  title: string
  body: string
  icon: string | null
  badge: string | null
  image: string | null
  url: string | null
  target_type: 'all' | 'role' | 'specific' | 'active'
  target_criteria: any
  scheduled_at: string | null
  sent_at: string | null
  total_sent: number
  total_delivered: number
  total_clicked: number
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed' | 'cancelled'
  created_by: string
  actions: any[]
  created_at: string
  updated_at: string
}

// PWA Settings Functions
export async function getPWASettings() {
  if (!supabase || !isSupabaseReady()) {
    // Return demo PWA settings
    return {
      data: [
        { key: 'pwa_install_button_enabled', value: true, description: 'Enable persistent install button' },
        { key: 'pwa_install_button_position', value: 'bottom-right', description: 'Button position' },
        { key: 'pwa_install_button_style', value: 'floating', description: 'Button style' },
        { key: 'pwa_install_button_text', value: 'Install App', description: 'Button text' },
        { key: 'pwa_install_button_icon', value: '📱', description: 'Button icon' },
        { key: 'vapid_public_key', value: 'BMBo_-QaSq8CVATH08n8lVmIJMIquffob9rPObHTc6VhkEn3CqXK2DYCMBcz_XHpEQun9J8DjKVfVJotjBRT7-E', description: 'VAPID public key for push notifications' },
        { key: 'vapid_private_key', value: 'I8kgaT61T7rW_rEp42_Eb1EL8NMbGXiClxH-JC65o6c', description: 'VAPID private key for push notifications' }
      ],
      error: null
    }
  }

  const { data, error } = await supabase
    .from('pwa_settings')
    .select('*')
    .order('key')

  return { data, error }
}

export async function getPWASetting(key: string) {
  if (!supabase || !isSupabaseReady()) {
    // Return demo setting based on key
    const demoSettings: Record<string, any> = {
      'pwa_install_button_enabled': true,
      'pwa_install_button_position': 'bottom-right',
      'pwa_install_button_style': 'floating',
      'pwa_install_button_text': 'Install App',
      'pwa_install_button_icon': '📱',
      'vapid_public_key': 'BMBo_-QaSq8CVATH08n8lVmIJMIquffob9rPObHTc6VhkEn3CqXK2DYCMBcz_XHpEQun9J8DjKVfVJotjBRT7-E',
      'vapid_private_key': 'I8kgaT61T7rW_rEp42_Eb1EL8NMbGXiClxH-JC65o6c'
    }
    
    return {
      data: { key, value: demoSettings[key] || null, description: 'Demo PWA setting' },
      error: null
    }
  }

  const { data, error } = await supabase
    .from('pwa_settings')
    .select('*')
    .eq('key', key)
    .single()

  return { data, error }
}

export async function updatePWASetting(key: string, value: any, description?: string) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('pwa_settings')
    .upsert({ key, value, description })
    .select()
    .single()

  return { data, error }
}

// Push Subscription Functions
export async function getAllPushSubscriptions() {
  if (!supabase || !isSupabaseReady()) {
    // Return demo subscriptions for testing + any real subscriptions from localStorage/global
    const demoSubscriptions = [
      {
        id: 'demo-subscription-123',
        user_id: 'demo-admin-123',
        endpoint: 'https://fcm.googleapis.com/fcm/send/demo-endpoint-123',
        p256dh: 'BMBo_-QaSq8CVATH08n8lVmIJMIquffob9rPObHTc6VhkEn3CqXK2DYCMBcz_XHpEQun9J8DjKVfVJotjBRT7-E',
        auth: 'I8kgaT61T7rW_rEp42_Eb1EL8NMbGXiClxH-JC65o6c',
        user_agent: 'Demo Browser (Chrome)',
        is_active: true,
        created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        updated_at: new Date().toISOString()
      },
      {
        id: 'demo-subscription-456', 
        user_id: 'demo-user-456',
        endpoint: 'https://fcm.googleapis.com/fcm/send/demo-endpoint-456',
        p256dh: 'BMBo_-QaSq8CVATH08n8lVmIJMIquffob9rPObHTc6VhkEn3CqXK2DYCMBcz_XHpEQun9J8DjKVfVJotjBRT7-E',
        auth: 'I8kgaT61T7rW_rEp42_Eb1EL8NMbGXiClxH-JC65o6c',
        user_agent: 'Demo Browser (Firefox)',
        is_active: true,
        created_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        updated_at: new Date().toISOString()
      },
      {
        id: 'demo-subscription-789',
        user_id: 'anonymous-visitor',
        endpoint: 'https://fcm.googleapis.com/fcm/send/demo-anonymous-789',
        p256dh: 'BMBo_-QaSq8CVATH08n8lVmIJMIquffob9rPObHTc6VhkEn3CqXK2DYCMBcz_XHpEQun9J8DjKVfVJotjBRT7-E',
        auth: 'I8kgaT61T7rW_rEp42_Eb1EL8NMbGXiClxH-JC65o6c',
        user_agent: 'Anonymous Mobile Visitor',
        is_active: true,
        created_at: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        updated_at: new Date().toISOString()
      }
    ]

    // Check for real subscriptions created during demo testing
    
    // Client-side: check localStorage
    if (typeof window !== 'undefined') {
      try {
        const storedSubs = localStorage.getItem('demo-real-subscriptions')
        if (storedSubs) {
          const realSubs = JSON.parse(storedSubs)
          demoSubscriptions.push(...realSubs)
          console.log(`📱 Found ${realSubs.length} real demo subscriptions in localStorage`)
        }
      } catch (error) {
        console.log('No real demo subscriptions found in localStorage')
      }
    }
    
    // Server-side: check global variable
    if (typeof global !== 'undefined' && global.demoSubscriptions) {
      try {
        demoSubscriptions.push(...global.demoSubscriptions)
        console.log(`📱 Found ${global.demoSubscriptions.length} real demo subscriptions in global storage`)
      } catch (error) {
        console.log('Error accessing global demo subscriptions')
      }
    }

    console.log(`📊 Demo mode: Returning ${demoSubscriptions.length} total subscriptions for testing`)
    return { data: demoSubscriptions, error: null }
  }

  const { data, error } = await supabase
    .from('push_subscriptions')
    .select('*')
    .order('created_at', { ascending: false })

  return { data, error }
}

export async function getPushSubscriptionStats() {
  if (!supabase || !isSupabaseReady()) {
    // Return stats for demo subscriptions
    const today = new Date().toISOString().split('T')[0]
    let todayCount = 1 // Demo subscription from today
    
    // Check for real subscriptions in localStorage
    if (typeof window !== 'undefined') {
      try {
        const storedSubs = localStorage.getItem('demo-real-subscriptions')
        if (storedSubs) {
          const realSubs = JSON.parse(storedSubs)
          const todaySubs = realSubs.filter((sub: any) => 
            sub.created_at && sub.created_at.startsWith(today)
          )
          todayCount += todaySubs.length
        }
      } catch (error) {
        console.log('Error checking localStorage subscriptions')
      }
    }

    return {
      data: {
        total_subscriptions: 3, // Demo subscriptions + any real ones
        active_subscriptions: 3,
        inactive_subscriptions: 0,
        subscriptions_today: todayCount
      },
      error: null
    }
  }

  try {
    const { count: total } = await supabase
      .from('push_subscriptions')
      .select('*', { count: 'exact', head: true })

    const { count: active } = await supabase
      .from('push_subscriptions')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    const today = new Date().toISOString().split('T')[0]
    const { count: today_count } = await supabase
      .from('push_subscriptions')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', today)

    return {
      data: {
        total_subscriptions: total || 0,
        active_subscriptions: active || 0,
        inactive_subscriptions: (total || 0) - (active || 0),
        subscriptions_today: today_count || 0
      },
      error: null
    }
  } catch (error) {
    return { data: null, error }
  }
}

// Notification Campaign Functions
export async function getNotificationCampaigns() {
  if (!supabase || !isSupabaseReady()) {
    return { data: [], error: null }
  }

  const { data, error } = await supabase
    .from('notification_campaigns')
    .select('*')
    .order('created_at', { ascending: false })

  return { data, error }
}

export async function createNotificationCampaign(campaign: Partial<NotificationCampaign>) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('notification_campaigns')
    .insert(campaign)
    .select()
    .single()

  return { data, error }
}

export async function deleteNotificationCampaign(id: string) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('notification_campaigns')
    .delete()
    .eq('id', id)

  return { data, error }
}

export async function getNotificationCampaignStats() {
  if (!supabase || !isSupabaseReady()) {
    return {
      data: {
        total_campaigns: 0,
        active_campaigns: 0,
        total_sent: 0,
        total_delivered: 0,
        total_clicked: 0,
        avg_delivery_rate: 0,
        avg_click_rate: 0
      },
      error: null
    }
  }

  try {
    const { count: total } = await supabase
      .from('notification_campaigns')
      .select('*', { count: 'exact', head: true })

    const { count: active } = await supabase
      .from('notification_campaigns')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'scheduled')

    return {
      data: {
        total_campaigns: total || 0,
        active_campaigns: active || 0,
        total_sent: 0,
        total_delivered: 0,
        total_clicked: 0,
        avg_delivery_rate: 0,
        avg_click_rate: 0
      },
      error: null
    }
  } catch (error) {
    return { data: null, error }
  }
}

export async function getNotifications() {
  if (!supabase || !isSupabaseReady()) {
    return { data: [], error: null }
  }

  const { data, error } = await supabase
    .from('notifications')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(100)

  return { data, error }
}

export async function getNotificationCampaign(id: string) {
  if (!supabase || !isSupabaseReady()) {
    return { data: null, error: null }
  }

  const { data, error } = await supabase
    .from('notification_campaigns')
    .select('*')
    .eq('id', id)
    .single()

  return { data, error }
}

export async function updateNotificationCampaign(id: string, updates: Partial<NotificationCampaign>) {
  if (!supabase || !isSupabaseReady()) {
    return createNotConfiguredError()
  }

  const { data, error } = await supabase
    .from('notification_campaigns')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

// Real-time subscriptions for PWA
export function subscribeToPushSubscriptionChanges(callback: (payload: any) => void) {
  if (!supabase || !isSupabaseReady()) {
    return { unsubscribe: () => {} }
  }

  return supabase
    .channel('push_subscription_changes')
    .on('postgres_changes', { event: '*', schema: 'public', table: 'push_subscriptions' }, callback)
    .subscribe()
}

export function subscribeToNotificationCampaignChanges(callback: (payload: any) => void) {
  if (!supabase || !isSupabaseReady()) {
    return { unsubscribe: () => {} }
  }

  return supabase
    .channel('notification_campaign_changes')
    .on('postgres_changes', { event: '*', schema: 'public', table: 'notification_campaigns' }, callback)
    .subscribe()
} 