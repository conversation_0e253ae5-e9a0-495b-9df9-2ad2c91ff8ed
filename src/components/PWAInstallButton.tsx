'use client'

import { useState, useEffect } from 'react'
import { getPWASetting } from '@/lib/database'

interface PWAInstallButtonProps {
  className?: string
}

interface DeviceInfo {
  browser: 'chrome' | 'safari' | 'firefox' | 'edge' | 'samsung' | 'unknown'
  os: 'ios' | 'android' | 'windows' | 'macos' | 'unknown'
  isStandalone: boolean
  supportsInstall: boolean
}

export default function PWAInstallButton({ className = '' }: PWAInstallButtonProps) {
  const [mounted, setMounted] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<Event | null>(null)
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(true)
  const [showInstructions, setShowInstructions] = useState(false)
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    browser: 'unknown',
    os: 'unknown',
    isStandalone: false,
    supportsInstall: false
  })
  const [settings, setSettings] = useState({
    enabled: true,
    position: 'bottom-right',
    style: 'floating',
    text: 'Install App',
    icon: '📱'
  })

  useEffect(() => {
    setMounted(true)
    loadSettings()
    detectDevice()
    
    // Handle PWA installation prompt (Chrome/Edge)
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setIsInstallable(true)
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setDeferredPrompt(null)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const detectDevice = () => {
    if (typeof window === 'undefined') return;
    
    const userAgent = navigator.userAgent.toLowerCase()
    
    // Detect browser
    let browser: DeviceInfo['browser'] = 'unknown'
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) browser = 'chrome'
    else if (userAgent.includes('safari') && !userAgent.includes('chrome')) browser = 'safari'
    else if (userAgent.includes('firefox')) browser = 'firefox'
    else if (userAgent.includes('edg')) browser = 'edge'
    else if (userAgent.includes('samsung')) browser = 'samsung'

    // Detect OS
    let os: DeviceInfo['os'] = 'unknown'
    if (/iphone|ipad|ipod/.test(userAgent)) os = 'ios'
    else if (/android/.test(userAgent)) os = 'android'
    else if (/windows/.test(userAgent)) os = 'windows'
    else if (/mac/.test(userAgent)) os = 'macos'

    // Check if already in standalone mode
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
      window.matchMedia('(display-mode: fullscreen)').matches ||
      window.matchMedia('(display-mode: minimal-ui)').matches ||
      (window.navigator as any).standalone === true

    // Determine if installation is supported
    const supportsInstall = browser === 'chrome' || browser === 'edge' || browser === 'samsung' || 
      (browser === 'safari' && (os === 'ios' || os === 'macos'))

    setDeviceInfo({
      browser,
      os,
      isStandalone,
      supportsInstall
    })

    setIsInstalled(isStandalone)
  }

  const loadSettings = async () => {
    try {
      const [enabled, position, style, text, icon] = await Promise.all([
        getPWASetting('pwa_install_button_enabled'),
        getPWASetting('pwa_install_button_position'),
        getPWASetting('pwa_install_button_style'),
        getPWASetting('pwa_install_button_text'),
        getPWASetting('pwa_install_button_icon')
      ])

      setSettings({
        enabled: enabled.data?.value || true,
        position: position.data?.value || 'bottom-right',
        style: style.data?.value || 'floating',
        text: text.data?.value || 'Install App',
        icon: icon.data?.value || '📱'
      })
    } catch (error) {
      console.error('Failed to load PWA button settings:', error)
    }
  }

  const handleInstall = async () => {
    if (deferredPrompt) {
      // Chrome/Edge installation
      ;(deferredPrompt as any).prompt()
      const { outcome } = await (deferredPrompt as any).userChoice
      console.log(`User response to install prompt: ${outcome}`)
      
      if (outcome === 'accepted') {
        setIsInstallable(false)
        setDeferredPrompt(null)
      }
    } else {
      // Show device-specific instructions
      setShowInstructions(true)
    }
  }

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed)
  }

  const getInstallInstructions = () => {
    if (deviceInfo.browser === 'safari' && deviceInfo.os === 'ios') {
      return {
        title: 'Install on iPhone/iPad',
        steps: [
          '1. Tap the Share button (□↗) at the bottom of Safari',
          '2. Scroll down and tap "Add to Home Screen"',
          '3. Tap "Add" to install the app'
        ],
        icon: '📱'
      }
    } else if (deviceInfo.browser === 'safari' && deviceInfo.os === 'macos') {
      return {
        title: 'Install on Mac',
        steps: [
          '1. Click "File" in the Safari menu',
          '2. Select "Add to Dock"',
          '3. The app will be added to your Dock'
        ],
        icon: '💻'
      }
    } else if (deviceInfo.browser === 'firefox') {
      return {
        title: 'Install on Firefox',
        steps: [
          '1. Click the address bar',
          '2. Look for the "Install" icon',
          '3. Click "Install" to add to your device'
        ],
        icon: '🦊'
      }
    } else if (deviceInfo.browser === 'chrome' || deviceInfo.browser === 'edge') {
      return {
        title: 'Install App',
        steps: [
          '1. Look for the install prompt',
          '2. Click "Install" when it appears',
          'Or check the address bar for an install icon'
        ],
        icon: '⬇️'
      }
    } else {
      return {
        title: 'Manual Installation',
        steps: [
          '1. Bookmark this page',
          '2. Add it to your home screen/desktop',
          '3. Use your browser\'s "Add to Home Screen" feature'
        ],
        icon: '📌'
      }
    }
  }

  // Don't render anything until component is mounted to prevent hydration mismatch
  if (!mounted) {
    return null
  }

  // Don't render if disabled in settings or already installed
  if (!settings.enabled || isInstalled) {
    return null
  }

  // Don't show if device doesn't support PWA installation
  if (!deviceInfo.supportsInstall) {
    return null
  }

  const getPositionClasses = () => {
    switch (settings.position) {
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'top-left':
        return 'top-4 left-4'
      case 'top-right':
        return 'top-4 right-4'
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2'
      default:
        return 'bottom-4 right-4'
    }
  }

  const getStyleClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-in-out z-50 backdrop-blur-sm'

    switch (settings.style) {
      case 'floating':
        return `${baseClasses} bg-blue-600/90 hover:bg-blue-700/95 text-white rounded-2xl shadow-lg hover:shadow-2xl border border-blue-500/20 hover:border-blue-400/30`
      case 'minimal':
        return `${baseClasses} bg-white/95 hover:bg-white text-gray-700 hover:text-gray-900 rounded-xl border border-gray-200/50 shadow-md hover:shadow-xl backdrop-blur-md`
      case 'gradient':
        return `${baseClasses} bg-gradient-to-br from-blue-500/90 via-purple-500/90 to-pink-500/90 hover:from-blue-600/95 hover:via-purple-600/95 hover:to-pink-600/95 text-white rounded-2xl shadow-lg hover:shadow-2xl border border-white/20`
      case 'glass':
        return `${baseClasses} bg-white/10 hover:bg-white/20 text-gray-900 dark:text-white rounded-2xl border border-white/20 hover:border-white/30 shadow-lg hover:shadow-xl backdrop-blur-md`
      default:
        return `${baseClasses} bg-blue-600/90 hover:bg-blue-700/95 text-white rounded-2xl shadow-lg hover:shadow-2xl border border-blue-500/20 hover:border-blue-400/30`
    }
  }

  const instructions = getInstallInstructions()

  return (
    <>
      <div className={`fixed ${getPositionClasses()} ${className}`}>
        {isCollapsed ? (
          // Collapsed state - enhanced floating button
          <button
            onClick={toggleCollapse}
            className={`${getStyleClasses()} p-4 group relative overflow-hidden`}
            title="Install App"
            aria-label="Open PWA installation options"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            <span className="text-2xl group-hover:scale-110 transition-all duration-300 relative z-10 drop-shadow-sm">
              {settings.icon}
            </span>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-lg"></div>
          </button>
        ) : (
          // Expanded state - modern card design
          <div className={`${getStyleClasses()} p-5 min-w-[240px] max-w-[280px] animate-in slide-in-from-bottom-2 duration-300`}>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <span className="text-lg">{settings.icon}</span>
                <span className="font-semibold text-sm">PWA Controls</span>
              </div>
              <button
                onClick={toggleCollapse}
                className="p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
                aria-label="Collapse PWA controls"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-3">
              {isInstallable ? (
                <button
                  onClick={handleInstall}
                  className="w-full bg-white/20 hover:bg-white/30 dark:bg-black/20 dark:hover:bg-black/30 rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 group border border-white/10 hover:border-white/20"
                >
                  <span className="group-hover:scale-110 transition-transform">{settings.icon}</span>
                  <span>{settings.text}</span>
                </button>
              ) : (
                <button
                  onClick={handleInstall}
                  className="w-full bg-white/20 hover:bg-white/30 dark:bg-black/20 dark:hover:bg-black/30 rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2 group border border-white/10 hover:border-white/20"
                >
                  <span className="group-hover:scale-110 transition-transform">{instructions.icon}</span>
                  <span>Install Instructions</span>
                </button>
              )}

              <div className="flex justify-between text-xs opacity-70 pt-2 border-t border-white/10">
                <a
                  href="/pwa-status"
                  className="hover:opacity-100 hover:underline transition-all px-2 py-1 rounded-md hover:bg-white/10"
                >
                  📊 Status
                </a>
                <a
                  href="/pwa-test"
                  className="hover:opacity-100 hover:underline transition-all px-2 py-1 rounded-md hover:bg-white/10"
                >
                  🧪 Test
                </a>
              </div>

              <div className="text-xs opacity-60 text-center pt-1 flex items-center justify-center gap-1">
                <span className="capitalize">{deviceInfo.browser}</span>
                <span>•</span>
                <span className="capitalize">{deviceInfo.os}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Installation Instructions Modal */}
      {showInstructions && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-in fade-in duration-300">
          <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-md w-full p-6 border border-gray-200 dark:border-gray-700 animate-in slide-in-from-bottom-4 duration-300">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                  <span className="text-2xl">{instructions.icon}</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {instructions.title}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Follow these steps to install
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowInstructions(false)}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                aria-label="Close instructions"
              >
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4 mb-6">
              {instructions.steps.map((step, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">
                    {index + 1}
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                    {step}
                  </p>
                </div>
              ))}
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setShowInstructions(false)}
                className="flex-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors font-medium"
              >
                Close
              </button>
              <a
                href="/pwa-status"
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl transition-colors text-center font-medium flex items-center justify-center gap-2"
              >
                <span>📊</span>
                <span>PWA Status</span>
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  )
} 