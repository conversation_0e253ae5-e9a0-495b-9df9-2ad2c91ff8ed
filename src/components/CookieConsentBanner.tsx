'use client';

import { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON>onsent from "react-cookie-consent";

export default function CookieConsentBanner() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to prevent hydration mismatch
  if (!mounted) {
    return null;
  }

  return (
    <CookieConsent
      location="bottom"
      buttonText="Accept"
      cookieName="cookieConsent"
      style={{ background: "#2B373B" }}
      buttonStyle={{ color: "#4e503b", fontSize: "13px" }}
      expires={150}
      onAccept={() => {
        window.location.reload(); // Reload to load GA scripts
      }}
    >
      This website uses cookies to enhance the user experience.
    </CookieConsent>
  );
} 