"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import Script from "next/script";
import Cookies from "js-cookie";

const GA_ID = "G-XXXXXXXXXX"; // Replace with your Google Analytics ID

export default function GoogleAnalytics() {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const [consented, setConsented] = useState(false);

  useEffect(() => {
    setMounted(true);
    setConsented(Cookies.get("cookieConsent") === "true");
  }, []);

  useEffect(() => {
    if (mounted && consented && typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag("config", GA_ID, { page_path: pathname });
    }
  }, [pathname, consented, mounted]);

  if (!mounted || !consented) return null;

  return (
    <>
      <Script src={`https://www.googletagmanager.com/gtag/js?id=${GA_ID}`} strategy="afterInteractive" />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GA_ID}');
        `}
      </Script>
    </>
  );
} 