'use client'

import { useState, useEffect } from 'react'
import { isSupabaseReady } from '@/lib/supabase'
import { getAuthStatus } from '@/lib/auth'
import { Card } from '@/components/ui/card'

export function ConfigStatus() {
  const [mounted, setMounted] = useState(false)
  const [status, setStatus] = useState({
    isSupabaseConfigured: false,
    isGoogleConfigured: false,
    isGitHubConfigured: false,
    isNextAuthConfigured: false,
    hasAnyProvider: false
  })

  useEffect(() => {
    setMounted(true)
    setStatus(getAuthStatus())
  }, [])

  // Don't render anything until component is mounted to prevent hydration mismatch
  if (!mounted) {
    return null
  }

  const allConfigured = status.isSupabaseConfigured && status.isNextAuthConfigured
  const hasProviders = status.isSupabaseConfigured || status.isGoogleConfigured || status.isGitHubConfigured

  if (allConfigured) {
    return null // Don't show if everything is configured
  }

  return (
    <Card className="p-6 mb-6 border-yellow-200 bg-yellow-50">
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0">
          <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">
            Template Configuration Status
          </h3>
          <p className="text-yellow-700 mb-4">
            This is a template preview. To unlock full functionality, configure the following:
          </p>
          
          <div className="space-y-2">
            <ConfigItem 
              name="Supabase Database" 
              configured={status.isSupabaseConfigured}
              description="Required for data persistence, authentication, and real-time features"
            />
            <ConfigItem 
              name="NextAuth Secret" 
              configured={status.isNextAuthConfigured}
              description="Required for secure authentication sessions"
            />
            <ConfigItem 
              name="Google OAuth" 
              configured={status.isGoogleConfigured}
              description="Optional: Enables Google sign-in"
            />
            <ConfigItem 
              name="GitHub OAuth" 
              configured={status.isGitHubConfigured}
              description="Optional: Enables GitHub sign-in"
            />
          </div>

          <div className="mt-4 p-3 bg-yellow-100 rounded-md">
            <p className="text-sm text-yellow-800">
              <strong>📚 Setup Instructions:</strong> Check the README.md file for detailed setup instructions, 
              or visit the{' '}
              <a 
                href="https://supabase.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="underline hover:text-yellow-900"
              >
                Supabase website
              </a>
              {' '}to create your project.
            </p>
          </div>
        </div>
      </div>
    </Card>
  )
}

function ConfigItem({ 
  name, 
  configured, 
  description 
}: { 
  name: string
  configured: boolean
  description: string
}) {
  return (
    <div className="flex items-center gap-2">
      <div className={`w-3 h-3 rounded-full ${configured ? 'bg-green-500' : 'bg-gray-300'}`} />
      <div className="flex-1">
        <span className={`font-medium ${configured ? 'text-green-800' : 'text-gray-600'}`}>
          {name}
        </span>
        <span className="text-sm text-gray-500 ml-2">
          {configured ? '✓ Configured' : '⚠ Not configured'}
        </span>
        <p className="text-xs text-gray-500 mt-1">{description}</p>
      </div>
    </div>
  )
}