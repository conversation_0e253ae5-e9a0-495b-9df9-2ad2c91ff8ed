'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface NotificationPermissionProps {
  className?: string
  showBanner?: boolean
}

export default function NotificationPermission({ className = '', showBanner = true }: NotificationPermissionProps) {
  const { data: session } = useSession()
  const [mounted, setMounted] = useState(false)
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPrompt, setShowPrompt] = useState(false)

  useEffect(() => {
    setMounted(true)
    checkNotificationStatus()
  }, []) // Remove session dependency - should work for all users

  useEffect(() => {
    // Re-check when session changes
    if (mounted) {
      checkNotificationStatus()
    }
  }, [session, mounted])

  const checkNotificationStatus = async () => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return
    }

    setPermission(Notification.permission)
    
    // Check if already subscribed
    if ('serviceWorker' in navigator && Notification.permission === 'granted') {
      try {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration) {
          const subscription = await registration.pushManager.getSubscription()
          setIsSubscribed(!!subscription)
        }
      } catch (error) {
        console.error('Error checking subscription status:', error)
      }
    }

    // Show prompt if user hasn't been asked (NO LOGIN REQUIRED!)
    if (Notification.permission === 'default' && showBanner) {
      setTimeout(() => setShowPrompt(true), 2000) // Show after 2 seconds
    }
  }

  const requestPermission = async () => {
    try {
      setIsLoading(true)
      setError('')

      if (!('Notification' in window)) {
        setError('Notifications are not supported in this browser')
        return
      }

      // Allow anonymous subscriptions for PWA experience
      // if (!session) {
      //   setError('Please log in first')
      //   return
      // }

      // Request permission
      const permission = await Notification.requestPermission()
      setPermission(permission)

      if (permission !== 'granted') {
        setError('Notification permission denied')
        return
      }

      // Register service worker
      let registration = await navigator.serviceWorker.getRegistration()
      if (!registration) {
        registration = await navigator.serviceWorker.register('/sw.js')
      }

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready

      // Check if already subscribed
      const existingSubscription = await registration.pushManager.getSubscription()
      if (existingSubscription) {
        setIsSubscribed(true)
        setShowPrompt(false)
        return
      }

      // Get VAPID public key from settings
      const vapidResponse = await fetch('/api/pwa/vapid-key')
      const vapidData = await vapidResponse.json()
      
      if (!vapidData.publicKey || vapidData.publicKey === '""') {
        setError('VAPID keys not configured. Please ask an admin to set up push notification keys.')
        return
      }

      // Create new subscription with proper VAPID key
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlB64ToUint8Array(vapidData.publicKey)
      })

      // Save subscription to server
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subscription: {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')!))),
              auth: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')!)))
            }
          },
          userAgent: navigator.userAgent
        })
      })

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to save subscription')
      }

      setIsSubscribed(true)
      setShowPrompt(false)

      // Show success notification
      new Notification('🎉 Notifications Enabled!', {
        body: 'You will now receive push notifications from this app.',
        icon: '/icons/icon-192x192.png'
      })

      console.log('✅ Push notification subscription successful:', result)

    } catch (error) {
      console.error('❌ Notification permission error:', error)
      setError(error instanceof Error ? error.message : 'Failed to enable notifications')
    } finally {
      setIsLoading(false)
    }
  }

  const unsubscribe = async () => {
    try {
      setIsLoading(true)
      
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        const subscription = await registration.pushManager.getSubscription()
        if (subscription) {
          await subscription.unsubscribe()
          
          // Notify server
          await fetch('/api/push/subscribe', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ endpoint: subscription.endpoint })
          })
        }
      }
      
      setIsSubscribed(false)
      console.log('✅ Unsubscribed from push notifications')
    } catch (error) {
      console.error('❌ Unsubscribe error:', error)
      setError('Failed to unsubscribe')
    } finally {
      setIsLoading(false)
    }
  }

  // Helper function to convert VAPID key
  function urlB64ToUint8Array(base64String: string) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/')
    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  if (!mounted || !('Notification' in window)) {
    return null
  }

  // Banner prompt
  if (showPrompt && permission === 'default') {
    return (
      <div className={`fixed top-0 left-0 right-0 bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 text-white p-4 z-50 shadow-lg backdrop-blur-sm border-b border-white/20 ${className}`}>
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                <span className="text-2xl">🔔</span>
              </div>
              <div>
                <p className="font-semibold text-lg">Stay in the Loop!</p>
                <p className="text-sm opacity-90 leading-relaxed">
                  Get instant notifications for important updates and never miss out.
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={requestPermission}
                disabled={isLoading}
                className="bg-white text-blue-600 px-6 py-3 rounded-xl font-semibold hover:bg-gray-100 disabled:opacity-50 transition-all duration-200 flex items-center gap-2 shadow-lg"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>Enabling...</span>
                  </>
                ) : (
                  <>
                    <span>✨</span>
                    <span>Enable Notifications</span>
                  </>
                )}
              </button>
              <button
                onClick={() => setShowPrompt(false)}
                className="text-white/80 hover:text-white text-sm px-3 py-2 rounded-lg hover:bg-white/10 transition-all duration-200"
              >
                Maybe Later
              </button>
            </div>
          </div>
          {error && (
            <div className="mt-3 p-3 bg-red-500/20 border border-red-400/30 rounded-lg backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-200">⚠️</span>
                <p className="text-sm text-red-100">{error}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Enhanced Status component (for settings pages, etc.)
  if (!showBanner) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-xl ${
              permission === 'granted' && isSubscribed
                ? 'bg-green-100 dark:bg-green-900/30'
                : permission === 'granted'
                ? 'bg-yellow-100 dark:bg-yellow-900/30'
                : permission === 'denied'
                ? 'bg-red-100 dark:bg-red-900/30'
                : 'bg-gray-100 dark:bg-gray-700'
            }`}>
              <span className="text-2xl">
                {permission === 'granted' && isSubscribed ? '🔔' :
                 permission === 'granted' ? '🔕' :
                 permission === 'denied' ? '🚫' : '⚪'}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Push Notifications</h3>
              <div className="flex items-center gap-2 mt-1">
                <div className={`w-2 h-2 rounded-full ${
                  permission === 'granted' && isSubscribed
                    ? 'bg-green-500'
                    : permission === 'granted'
                    ? 'bg-yellow-500'
                    : permission === 'denied'
                    ? 'bg-red-500'
                    : 'bg-gray-400'
                }`}></div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {permission === 'granted' && isSubscribed ? 'Enabled and active' :
                   permission === 'granted' ? 'Permission granted, not subscribed' :
                   permission === 'denied' ? 'Permission denied' :
                   'Not enabled'}
                </p>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            {permission !== 'granted' && (
              <button
                onClick={requestPermission}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50 transition-colors flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Enabling...</span>
                  </>
                ) : (
                  <>
                    <span>🔔</span>
                    <span>Enable</span>
                  </>
                )}
              </button>
            )}
            {isSubscribed && (
              <button
                onClick={unsubscribe}
                disabled={isLoading}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium disabled:opacity-50 transition-colors flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Unsubscribing...</span>
                  </>
                ) : (
                  <>
                    <span>🔕</span>
                    <span>Unsubscribe</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
        {error && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-red-500">⚠️</span>
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        )}
      </div>
    )
  }

  return null
} 