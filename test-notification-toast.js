// Test script for notification toast
console.log('�� Testing Notification Toast');

// Step 1: Check current state
console.log('Current Notification permission:', Notification.permission);
console.log('Has been prompted before:', localStorage.getItem('notification-toast-prompted'));

// Step 2: Reset for testing
function resetTest() {
  localStorage.removeItem('notification-toast-prompted');
  console.log('✅ Reset localStorage - refresh page to see toast');
}

// Step 3: Force show toast (for testing)
function forceShowToast() {
  // This simulates fresh user
  localStorage.removeItem('notification-toast-prompted');
  location.reload();
}

// Make functions available globally
window.resetTest = resetTest;
window.forceShowToast = forceShowToast;

console.log('🔧 Test functions available:');
console.log('- resetTest() - Clears localStorage');
console.log('- forceShowToast() - Resets and reloads page');
